<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Account_switch extends Auth_Controller {

	public function index()
	{
		//get list account
		$request = $this->api_request();
		$response = $request[0];
		$ci = $request[1];
		// ------------------------------------------------------------------------
		if (!is_json($response)) {
			http_response_code(500);
			show_error('Please logout and login to switch account.', $ci['http_code'], 'Oops..! Switch Account Failed');
			die;
		}

		$data = json_decode($response, true);
		$list_account = $data['accounts'] ?? [];
		$token = $data['token'] ?? '';

		$page['token'] = $token;
		$page['list_account'] = $list_account;
		$page['page_title'] = 'Switch Account';
		$this->template->view('account/account_switch_v', $page);
	}

	public function process()
	{
		$input_token = $this->input->post('token');
		$input_business_id = $this->input->post('business');
		$input_user_id = $this->input->post('user');
		$input_user_type = $this->input->post('user_type');

		//validate token
		// ------------------------------------------------------------------------
		//get list account
		$request = $this->api_request();
		$response = $request[0];
		$ci = $request[1];

		$data = json_decode($response, true);
		$list_account = $data['accounts'];

		$data_map = [];
		foreach ($list_account as $account) {
			$business_id = $account['business_id'];
			$business_activation_status = $account['business_activation_status'] ?? '';
			$user_id = $account['user_id'];
			$user_type = $account['user_type'];

			//add data to map when business activated
			if ($business_activation_status == 'activated') {
				$data_map[$business_id][$user_type][$user_id] = $account;
			}
		}

		//check data on map?
		if (!empty($data_map[$input_business_id][$input_user_type][$input_user_id])) {

			$data = $data_map[$input_business_id][$input_user_type][$input_user_id];
			$business_name = $data['business_name'];

			//create session
			$this->authlib->create_user_session_by_type_and_id($input_user_type, $input_user_id);

			//change rememberme
			$rememberme = get_cookie($this->config->item('remember_cookie_name','auth'));
			if (!empty($rememberme)) {
				# remove old remember me
				$this->authlib->clear_remember($rememberme);

				# generate new remember me
				$this->authlib->create_remember($input_user_type, $input_user_id);
			}

			//create token report
			$this->api_report->clear(); # delete previous user report token
			// $this->api_report->get_new_token(); //will create after refresh page

			//remove old other cookie
			delete_cookie('uniq-api');
			delete_cookie('uniq-report');
			delete_cookie('uniq-chat-api');

			echo format_json(['message' => 'Success']);
			die;
		}

		http_response_code(404);
		echo format_json(['message' => 'Selected Account Not Exist']);
	}

	public function api_request()
	{
		//get cookie
		$cookieb64 = get_cookie('uniq-api');
		$cookiejson = base64_decode($cookieb64);
		$cookie_data = json_decode($cookiejson, true);
		$token = $cookie_data['token'] ?? '';

		//get list account
		$curl = curl_init();
		curl_setopt_array($curl, array(
			CURLOPT_URL => $this->api . '/auth/v1/account',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTPHEADER => array(
				'Authorization: '.$token,
			),
		));

		$response = curl_exec($curl);
		$ci = curl_getinfo($curl);

		curl_close($curl);

		return [$response, $ci];
	}

}

/* End of file Account_switch.php */
/* Location: ./application/modules/settings_user/controllers/Account_switch.php */