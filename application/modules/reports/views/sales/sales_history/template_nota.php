<!DOCTYPE html>
<html lang="id">

<head>
  <meta charset="UTF-8" />
  <title>Invoice</title>
  <style>
    @page {
      background-image-resize: 6;
      /* Resize image agar pas */
    }

    body {
      font-family: Arial, sans-serif;
      font-size: 12px;
    }

    .container {
      padding: 20px;
    }

    .bill-to {
      width: 100%;
      margin-bottom: 10px;
    }

    .bill-to td {
      padding: 5px;
      vertical-align: top;
    }

    .table-items {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .table-items th,
    .table-items td {
      border-bottom: 1px solid #f2f2f2;
      padding: 8px;
      text-align: left;
    }

    .table-items th {
      background-color: #f2f2f2;
    }

    .totals,
    .payment-method {
      width: 100%;
      margin-top: 15px;
    }

    .totals td {
      padding: 5px;
      text-align: right;
    }

    .payment-method td {
      padding: 5px;
    }

    .footer {
      text-align: left;
      font-size: 10px;
      margin-top: 15px;
    }

    .company-logo {
      width: 100%;
      text-align: left;
    }

    .company-logo img {
      width: 120px;
    }

    .header-logo {
      height: 3.5rem;
    }

    .table-header {
      width: 100%;
    }

    .badge-status {
      background-color: #ff6b6b;
      color: white;
      font-size: 14px;
      font-weight: bold;
      padding-top: 5em;
      padding-left: 15em;
      padding-bottom: 5em;
      padding-right: 15em;
      border-top-left-radius: 4em;
      border-top-right-radius: 4em;
      border-bottom-right-radius: 4em;
      border-bottom-left-radius: 4em;
      display: inline-block;
    }

    .table-header td {
      vertical-align: middle;
    }

    .badge-container {
      text-align: right;
      width: 50%;
    }
  </style>
</head>

<body>
  <div class="container">
    <table class="table-header">
      <tr>
        <td>
          <img
            class="header-logo"
            src="<?= $image_url ?>"
            style="max-width: 100px; max-height: 100px;"
            alt="logo" />
        </td>
        <td class="badge-container">
          <span class="badge-status" style="padding: 8px 15px"><?= $data_nota[0]->payment_status ?></span>
        </td>
      </tr>
    </table>

    <table style="width: 100%">
      <tr>
        <td>
          <table class="company-logo">
            <tr>
              <td style="text-align: left">
                <strong style="font-size: 1.75rem"><?= $outlet->name ?></strong>
                <div style="margin-top: 1rem">
                  <?= $outlet->address ?>
                  <br>
                  <?= $outlet->receipt_phone ?>
                </div>
              </td>
            </tr>
          </table>
        </td>
        <td style="width: 50%; text-align: right">
          <strong style="font-size: 1.75rem">INVOICE</strong>
          <div style="margin-top: 1rem">
            <strong>Invoice#</strong> <?= $data_nota[0]->nomor_nota ?><br />
            <strong>Invoice Date:</strong> <?= $data_nota[0]->tanggal ?><br />
            <strong>Due Date:</strong> <?= $data_nota[0]->tanggal ?>
          </div>
        </td>
      </tr>
    </table>

    <div style="margin-bottom: 1rem; margin-top: 2rem">
      <strong style="font-size: 1.5rem">Bill To:</strong><br /><?= $data_nota[0]->customer_name ?>
      <br /><?= $data_nota[0]->member_address ?>
    </div>

    <table class="table-items">
      <thead>
        <tr>
          <th>#</th>
          <th>Item & Description</th>
          <th>Qty</th>
          <th>Disc. Info</th>
          <th>Amount (IDR)</th>
        </tr>
      </thead>
      <tbody>
        <?php $discount = 0;  ?>
        <?php foreach ($data_nota as $key => $nota): ?>
          <?php $discount += $nota->discount; ?>
          <tr>
            <td>
              <?= $key + 1 ?>
            </td>
            <td>
              <table style="border: none;">
                <tr>
                  <td style="vertical-align: top; padding-right: 8px;">
                    <img
                      src="<?= $nota->product_image; ?>"
                      style="max-width: 40px; max-height: 40px;"
                      alt="logo" />
                  </td>
                  <td style="vertical-align: top;">
                    <div>
                      <span><?= $nota->product_name; ?></span><br />
                      <small style="color: grey;"><?= $nota->product_description ?></small>
                    </div>
                  </td>
                </tr>
              </table>
            </td>
            <td>
              <?= $nota->qty ?>
            </td>
            <td>
              <?= $nota->keterangan_discount ?>
            </td>
            <td>
              Rp. <?= number_format($nota->price, 0, ',', '.') ?>
            </td>
          </tr>
        <?php endforeach; ?>
      </tbody>
    </table>

    <table class="totals">
      <tr>
        <td><strong>Sub Total:</strong></td>
        <td>Rp. <?= number_format($sub_total, 0, ",", ".") ?></td>
      </tr>
      <tr>
        <td><strong>Discount:</strong></td>
        <td>Rp. <?= number_format($discount, 0, ",", ".") ?></td>
      </tr>
      <tr>
        <td><strong>Tax:</strong></td>
        <td>Rp. <?= number_format($total_tax, 0, ",", ".") ?></td>
      </tr>
      <tr>
        <td><strong>Total:</strong></td>
        <td><strong>Rp. <?= number_format($data_nota[0]->grand_total, 0, ",", ".") ?></strong></td>
      </tr>
      <!-- <tr>
        <td><strong>Payment Retention (-):</strong></td>
        <td>IDR 10.00</td>
      </tr>
      <tr>
        <td><strong>Payment Made (-):</strong></td>
        <td>IDR 100.00</td>
      </tr>
      <tr>
        <td><strong>Balance Due:</strong></td>
        <td><strong>IDR 562.75</strong></td>
      </tr> -->
    </table>

    <hr />

    <?php if ($data_nota[0]->payment_status != "paid"): ?>
      <!-- <div class="footer">
        <table class="payment-method">
          <tr>
            <td>
              <strong style="font-size: 1.5rem">METODE PEMBAYARAN: BANK TRANSFER</strong>
            </td>
          </tr>
          <tr>
            <td style="font-size: 1rem">
              Silakan transfer pembayaran hanya pada rekening berikut:
            </td>
          </tr>
          <tr>
            <td style="font-size: 1rem">
              <strong>Bank:</strong> BCA<br />
              <strong>No Rekening:</strong> **********<br />
              <strong>Atas Nama:</strong> APLIKASI UNIQ INDONESIA
            </td>
          </tr>
        </table>
      </div> -->
    <?php endif; ?>
  </div>
</body>

</html>