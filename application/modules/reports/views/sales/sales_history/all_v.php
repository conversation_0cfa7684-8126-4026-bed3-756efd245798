<style type="text/css">
  .tooltip-inner {
    white-space: pre-wrap;
    width: 350px;
  }

  div.dt-buttons {
    float: right;
    margin-left: 10px;
  }

  .dropdown-menu-left {
    right: auto;
    /* Reset nilai right */
    left: 0;
    /* <PERSON><PERSON><PERSON>n di sebelah kiri */
  }

  .wrapper-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .wrapper-header h4 {
    color: #333;
  }

  /* Search Panel Styles */
  #searchPanel {
    position: fixed;
    right: -300px;
    top: 0;
    width: 300px;
    height: 100%;
    background-color: #333;
    border-left: 1px solid #ddd;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    padding: 0;
    overflow-y: auto;
    transition: right 0.3s ease;
  }

  #searchPanel.active {
    right: 0;
    color: #ffff
  }

  #searchPanel h4 {
    margin: 0;
    padding: 15px;
    background-color: #333;
    color: #f8d62b;
    font-weight: bold;
    position: relative;
  }

  #searchPanel .panel-content {
    padding: 15px;
  }

  #searchPanel .form-group {
    margin-bottom: 15px;
  }

  #searchPanel .form-control {
    color: #ffff;
  }

  #searchPanel .btn-add-search {
    margin-bottom: 15px;
    background-color: #333;
    color: white;
    border: none;
  }

  #searchPanel .btn-panel-close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 18px;
    color: white;
    background: none;
    border: none;
  }

  #searchPanel .panel-buttons {
    margin-top: 20px;
    text-align: center;
  }

  #searchPanel .panel-buttons .btn-primary {
    background-color: #3379b7;
    border-color: #2e6da4;
  }

  #searchPanel .panel-buttons .btn-default {
    background-color: #f0ad4e;
    border-color: #eea236;
    color: white;
  }

  #searchPanel input,
  #searchPanel select {
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: #333;
  }

  #searchPanel select.column-select {
    margin-bottom: 10px;
  }
</style>
<?php if ($promotion_id != "") {
  $option = $promotion_id;
  $text = "promotion";
} else if ($customer != "") {
  $option = $customer;
  $text = "customer_name :";
} else {
  $option = "";
  $text = "";
} ?>
<div id="app">
  <div class="row pull-right" style="margin-bottom: 5px" id="nav-dekstop-sukses">
    <div class="col-sm-12">
      <div class="btn-group">
        <?php if ($text != "" || $option != "") { ?>
          <a href="<?= getenv("APP_URL") ?>reports/sales/history?tab=transaction" class="btn btn-info filterclose">
            <?= $text ?> <?= $option ?>&nbsp;<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
              fill="currentColor" class="bi bi-x-circle-fill" viewBox="0 0 16 16">
              <path
                d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z" />
            </svg>
          </a>
        <?php } ?>
      </div>
      <div class="btn-group">
        <button type="button" class=" btn btn-info daterange" id="date-all">
          <span>
            <i class="glyphicon glyphicon-calendar"></i> Date
          </span>
          <i class="caret"></i>
        </button>
      </div>

      <div class="btn-group">
        <select class="form-control btn btn-info selectpicker outletSelect" multiple id="select_outlet_all"
          name="select_outlet" v-model="inputs.outlet">
          <?php foreach ($form_select_outlet as $a) : ?>
            <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
          <?php endforeach ?>
        </select>
      </div>
      <div class="btn-group">
        <select class="form-control btn btn-info selectpicker shiftSelect" multiple id="select_shift_all"
          name="select_shift" v-model="inputs.shift">
          <?php foreach ($form_select_shift as $a) : ?>
            <option value="<?= $a->shift_id ?>"><?= htmlentities($a->name) ?></option>
          <?php endforeach ?>
        </select>
      </div>
      <div class="btn-group">
        <select class="form-control btn btn-info" id="data-status" name="data-status" v-model="inputs.dataStatus">
          <option value="all">Semua</option>
          <option value="success">Success</option>
          <option value="refund">Refund</option>
        </select>
      </div>
      <div class="btn-group">
        <select class="form-control btn btn-info" id="data_type_trans" name="data_type" v-model="inputs.dataType">
          <option value="1">By Date</option>
          <option value="0">By Shift</option>
        </select>
      </div>
      <div class="btn-group">
        <button class="btn btn-primary btn-block" id="btnTransaksi" @click="onApplay" type="button">Apply</button>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12 table-responsive">
      <table id="table-transaksi" class="table table-striped table-report" cellspacing="0" width="100%">
        <thead>
          <tr>
            <th>No</th>
            <th>No Order</th>
            <th>Outlet</th>
            <th>Shift</th>
            <th>Date</th>
            <th>Status</th>
            <th>Payment</th>
            <th>Cash</th>
            <th>Card</th>
            <th>Piutang</th>
            <th>Duty Meals</th>
            <th>Compliment</th>
            <th>PAX</th>
            <th>Operator</th>
            <th>Sub Total</th>
            <th>Discount total</th>
            <th>Discount Info.</th>
            <th>Voucher</th>
            <th>Voucher Info.</th>
            <th>Promo</th>
            <th>Promo Info.</th>
            <th>TAX</th>
            <th>Service</th>
            <th>Order Type</th>
            <th>Customer</th>
            <th>Grand Total</th>
            <th>#</th>
          </tr>
        </thead>
        <br>
        <tfoot class="index1" style="background: #b7b6b6;">
          <tr>
            <th>Total</th>
            <?php for ($i = 0; $i < 26; $i++) { ?>
              <th></th>
            <?php } ?>
          </tr>
          <tr>
            <th>Total/Halaman1</th>
            <?php for ($i = 0; $i < 26; $i++) { ?>
              <th></th>
            <?php } ?>
          </tr>
        </tfoot>

      </table>
    </div>
  </div>
  <div v-if="showCancelBtn == true" class="cancel-button-wrapper">
    <button @click="cancelOperation" class="btn-cancel">Cancel</button>
  </div>

  <!-- Search Panel -->
  <div id="searchPanel">
    <h4>Column Search Filter <button class="btn-panel-close" id="closeSearchPanel">&times;</button></h4>
    <div class="panel-content">
      <div id="defaultSearchFields">
        <!-- Default search fields will be added here dynamically -->
      </div>
      <div class="form-group">
        <button class="btn btn-sm btn-add-search" id="addSearchField">
          <i class="fa fa-plus"></i> Add Column
        </button>
      </div>
      <div id="additionalSearchFields">
        <!-- Additional search fields will be added here dynamically -->
      </div>
      <div class="panel-buttons">
        <button class="btn btn-primary" id="applySearch">Search</button>
        <button class="btn btn-default" id="clearSearch">Clear Search</button>
      </div>
    </div>
  </div>

  <div id="pdfModal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <div class="wrapper-header">
            <h4 class="modal-title">Preview</h4>
            <div>
              <button type="button" class="btn btn-success" id="btn-print">Print</button>
              <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
        <div class="modal-body">
          <div id="pdfFrame" class="pdf-container"></div>
          <iframe id="pdf-frame" style="width: 100%; height: 500px;"></iframe>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .pdf-container {
    width: 100%;
    max-height: 500px;
    overflow-y: auto;
    background: #f5f5f5;
    border: 1px solid #ccc;
    padding: 10px;
  }

  canvas {
    width: 100% !important;
    height: auto !important;
    margin-bottom: 10px;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
  }

  .wrapper-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .modal-lg {
    max-width: 90%;
  }

  .index1 {
    visibility: visible;
  }

  .buttons-colvis {
    background: #ff9e01 !important;
    border: 1px solid #ff9e01;
  }

  .cancel-button-wrapper {
    width: 100vw;
    height: 100%;
    position: fixed;
    z-index: 99999999;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background-color: #ff9e01; */
  }

  .btn-cancel {
    padding: 6px 12px;
    font-size: 2rem;
    margin-top: 13rem;
    border-radius: 5px;
    outline: none;
    background: transparent !important;
    transition: .1s ease-in-out;
    color: #ff9e01 !important;
    border: 1px solid #ff9e01;
  }

  .btn-cancel:hover {
    background-color: #ff9e01 !important;
    color: #333 !important;
  }

  @media (max-width: 767.98px) {
    .index1 {
      visibility: hidden;
      display: none;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
      padding: 0 !important;
    }
  }
</style>


<script src="https://mozilla.github.io/pdf.js/build/pdf.mjs" type="module"></script>
<script type="module">
  window.renderPDFWithBlob = async function(blob) {

    var {
      pdfjsLib
    } = globalThis;

    // The workerSrc property shall be specified.
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://mozilla.github.io/pdf.js/build/pdf.worker.mjs';

    const arrayBuffer = await blob.arrayBuffer();
    const pdfViewer = document.getElementById("pdfFrame");
    pdfViewer.innerHTML = "";

    const loadingTask = pdfjsLib.getDocument({
      data: arrayBuffer
    });

    const pdf = await loadingTask.promise;

    for (let pageNumber = 1; pageNumber <= pdf.numPages; pageNumber++) {
      const page = await pdf.getPage(pageNumber);

      const scale = 1.5;
      const viewport = page.getViewport({
        scale: scale
      });

      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      canvas.style.margin = '1rem auto';
      canvas.style.display = 'block';
      canvas.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
      canvas.style.borderRadius = '8px';
      canvas.style.border = '1px solid #ddd';
      canvas.style.background = 'white';

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      await page.render(renderContext).promise;
      pdfViewer.appendChild(canvas);
    }
  };
</script>
<script type="text/javascript">
  var isAiDataSent = false;
  var reportData = "";
  console.log('chainlit-call-fn init..');
  //used for chat copilot
  window.addEventListener("chainlit-call-fn", (e) => {
    const {
      name,
      args,
      callback
    } = e.detail;
    console.log('chainlit-call-fn got called, name: ' + name);
    if (name === "report_data") {
      console.log('send data size: ', reportData.length)
      callback(reportData);
    }
  });


  var vm = new Vue({
    el: "#app",
    data: {
      isUsingDates: false,
      showCancelBtn: false,
      isCancel: false,
      dataPayload: {},
      table: '',
      no: 1,
      isLoadingFromCache: false,
      inputs: {
        startDate: '',
        endDate: '',
        timeZone: '',
        outlet: [],
        shift: [],
        dataType: '1',
        limit: 50,
        offset: 0,
        page: 0,
        dataStatus: 'all',
        offsetId: 0,
      }
    },
    methods: {
      onApplay() {

        vm.isUsingDates = true;
        vm.isCancel = false;
        let outletSelectLength = $('#select_outlet_all option').length;

        this.saveFiltersToCache();

        if (vm.inputs.outlet.length == 0 && outletSelectLength > 1) {
          Alert.confirm("Memilih Semua Outlet",
            "Proses Ini Mungkin Membutuhkan Sedikit Waktu, Pastikan Koneksi Anda Dalam Keadaan Baik").then(
            function(
              result) {
              if (!result.value) {
                return;
              }
              vm.showCancelBtn = true
              loading.show();
              if (this.table != '') {
                vm.resetState()
              }
              // vm.getData()
              vm.appendTable()
              vm.exportAllCurrent()


            })
        } else {

          vm.showCancelBtn = true
          loading.show();
          // vm.getData()
          if (this.table != '') {
            vm.resetState()
          }
          this.appendTable()
          this.exportAllCurrent()

        }
      },
      dataTable() {
        $(function() {
          vm.table = $('#table-transaksi').DataTable({
            scrollY: 'auto',
            scrollX: $(window).width() < 768 ? false : true,
            paging: true,
            scrollCollapse: true,
            serverSide: false,
            destroy: true,
            responsive: $(window).width() < 768 ? true : false,

            fixedColumns: {
              leftColumns: 3,
            },

            columnDefs: [{
                targets: [1],
                type: 'formatted-num',
                render: function(data, type, columns, meta) {
                  if (type === 'display') {

                    data =
                      '<a target="_blank" href="<?= getenv("APP_URL") ?>/reports/sales/history?tab=detail&id=' +
                      data + '"> ' + data + ' </a>';

                  }

                  return data;
                },

              },
              {
                // Hide payment method columns by default
                targets: [7, 8, 9, 10, 11],
                visible: false
              }
            ],
            order: [
              [1, 'asc']
            ],
            dom: "<'row'<'col-sm-6'l><'col-sm-6'Bf>>" +
              "<'row'<'col-sm-12'tr>>" +
              "<'row'<'col-sm-5'i><'col-sm-7'p>>", //buat custom toolbar

            dom: 'Bfltip',
            stateSave: true,
            buttons: [{
                extend: 'colvis',
                className: 'btn dark btn-outline',
                text: '<i class="fa fa-cog fa-spin" aria-hidden="true"></i>'
              },
              {
                text: '<i class="fa fa-filter" aria-hidden="true"></i>',
                className: 'btn dark btn-outline',
                action: function() {
                  // Show the search panel
                  $('#searchPanel').addClass('active');
                }
              },
              {
                extend: 'collection',
                text: 'EXPORT',
                buttons: [{
                    extend: 'pdfHtml5',
                    orientation: 'landscape',
                    customize: function(doc) {
                      doc.defaultStyle.fontSize = 8.5; //<-- set fontsize to 16 instead of 10 
                      doc.styles.tableHeader.fontSize = 8;
                      doc.styles.tableFooter.fontSize = 8;
                      //  doc.styles.title.fontSize = 7;
                    },

                    pageSize: 'legal',
                    title: "REPORT SALES HISTORY TRANSAKSI",
                    filename: vm.exportAllCurrent(),
                    footer: true,
                    // footer:true,

                    exportOptions: {
                      columns: [0, ':visible']
                    }
                  },
                  {
                    extend: 'excelHtml5',
                    title: vm.exportAllCurrent(),
                    footer: true,
                    messageTop: "REPORT SALES HISTORY TRANSAKSI",
                    // footer:true,
                    exportOptions: {
                      // columns: (function() {
                      //     var cols = [];
                      //     for (var i = 0; i <= 24; i++) {
                      //         cols.push(i);
                      //     }
                      //     return cols;
                      // })(),
                      columns: [0, ':visible'],
                      format: {
                        body: function(data, row, column, node) {
                          data = $('<p>' + data + '</p>').text();
                          return $.isNumeric(data.replace(
                              /[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '')) ?
                            numeral(data).value() : data;
                        }
                      }
                    }
                  },
                ],
              },
              <?php if (getenv("CI_ENV") == "development" || getenv("ENABLE_AI_ASSISTANT") == "true") { ?> {
                  text: '', // Leave empty as we'll use custom HTML
                  className: 'ai-button-container',
                  action: function(e, dt, node, config) {
                    // Get data rows
                    var dataRows = dt.data().toArray();
                    console.log('total size data', dataRows.length);
                    // console.log(JSON.stringify(dataRows))
                    if (dataRows.length == 0) {
                      Alert.error('Belum Menampilkan Data', 'Klik tombol "Apply" untuk menampilkan data terlebih dahulu!');
                      return;
                    }
                    window.location.hash = 'new-hash';
                    if (typeof window.openCoplot !== 'function') {
                      alert('UNIQ Chat Copilot is not loaded! try to reload the page');
                    }

                    window.changeCopilotButton();
                    setTimeout(() => {
                      window.openCoplot(); //opening chatbox                                                                         
                    }, 500);

                    if (isAiDataSent) {
                      return;
                    }

                    console.log('getting headers..');
                    // Get headers (clean up the text to use as keys)
                    var allheaders = dt.columns().header().toArray().map(function(header) {
                      return $(header).text().trim().toLowerCase()
                        .replace(/\s+/g, '_') // Replace spaces with underscores
                        .replace(/[^a-z0-9_]/g, ''); // Remove special characters
                    });
                    var headers = allheaders.filter(item => item !== '' && item !== '#');

                    console.log('mapping data to header...')
                    // Transform data into array of objects with header keys
                    var structuredData = dataRows.map(function(row) {
                      var rowObject = {};
                      headers.forEach(function(header, index) {
                        //remove any html 
                        if ((typeof row[index] === 'string' || row[index] instanceof String) && row[index].toString().startsWith('<')) {
                          row[index] = stripHtml(row[index]);
                        }
                        if ((typeof row[index] === 'string' || row[index] instanceof String) && (row[index].toString().startsWith('IDR') || row[index].toString().startsWith('Rp'))) {
                          // console.log(`remove idr from data: ${row[index]}`)
                          row[index] = numeral(row[index]).value()
                        }
                        //pax usually have format: 1.0 
                        if (header == "pax") {
                          row[index] = numeral(row[index]).value()
                        }

                        rowObject[header] = row[index];
                      });
                      return rowObject;
                    });

                    // console.log('data: ', JSON.stringify(structuredData));

                    setTimeout(() => {
                      window.sendChainlitMessage({
                        type: "user_message",
                        name: "analysis_request",
                        output: `Lakukan analisa pada laporan Riwayat Transaksi berikut!`,
                      });
                    }, 1000);

                    // const markdwn = createMarkdownTable(structuredData);
                    setTimeout(() => {
                      console.log('converting data to json...');
                      var dataJson = JSON.stringify(structuredData);
                      reportData = dataJson;
                      console.log('data converted...', dataJson.length);
                      // console.log(dataJson)
                      // const code = "```";
                      // window.sendChainlitMessage({
                      //     type: "user_message",
                      //     name: "analysis_request",
                      //     metadata: {
                      //         category: "sales",
                      //         data: '[JSON]'
                      //     },
                      //     output: 'markdown', //markdwn
                      //     wait_for_answer: true,
                      //     tags: ["report", "analysis"]
                      //     //\n ## Data Laporan (JSON) \n ${code} ${dataJson} ${code} \n\n Lakukan analisa dan berikan insight dari data laporan penjualan di atas, kemudian berikan pertanyaan follow-up!
                      // });  

                      window.sendChainlitMessage({
                        type: "assistant_message",
                        name: "analysis_request_data",
                        output: "loading...", //markdwn                                                            
                      });

                      window.changeCopilotButton();
                      isAiDataSent = true;
                    }, 3500);
                  },
                  init: function(api, node, config) {
                    // Replace button content with our custom HTML
                    $(node).html(aiButtonHtml);
                  }
                },
              <?php } ?>
            ],

            "footerCallback": async function(row, data, start, end, display) {
              var api = this.api();
              // console.log(data)

              // Remove the formatting to get integer data
              var intVal = function(i) {
                return typeof i === 'string' ?
                  i.replace(/[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '') * 1 :
                  typeof i === 'number' ? i : 0;
              };

              // Helper function for total calculations
              var totalPage = function(type, colom) {
                if (type == 'total') {
                  return api.column(colom).data().reduce(function(a, b) {
                    return numeral(a).value() + numeral(b).value();
                  }, 0);
                } else {
                  return api.column(colom, {
                    page: 'current'
                  }).data().reduce(function(a, b) {
                    return numeral(a).value() + numeral(b).value();
                  }, 0);
                }
              }

              // Update footer values for each column
              // Format: [column_index, format_function]
              const columnsToTotal = [
                [7, number], // Pax
                [9, currency], // Sub Total
                [10, currency], // Discount Total
                [12, currency], // Voucher
                [14, currency], // Promo
                [16, currency], // Tax
                [17, currency], // Service
                [20, currency] // Grand Total
              ];

              // Add empty cells for non-numeric columns
              const emptyColumns = [1, 2, 3, 4, 5, 6, 8, 11, 13, 15, 18, 19]; // Columns that should be empty
              emptyColumns.forEach(colIndex => {
                $('tr:eq(0) th:eq(' + colIndex + ')', api.table().footer()).html('');
                $('tr:eq(1) th:eq(' + colIndex + ')', api.table().footer()).html('');
              });

              // Update both current page total and overall total
              columnsToTotal.forEach(([colIndex, formatFn]) => {
                // Current page total
                $('tr:eq(0) th:eq(' + colIndex + ')', api.table().footer())
                  .html(formatFn(totalPage('page', colIndex + 5)));

                // Overall total
                $('tr:eq(1) th:eq(' + colIndex + ')', api.table().footer())
                  .html(formatFn(totalPage('total', colIndex + 5)));
              });
            },

          });
          //   vm.table.on('#table-transaksi tbody').on('click', 'tr', function () {
          //     var data =   vm.table.row(this).data();
          //     let newUrl = "http://localhost:8000/reports/sales/history?tab=detail&id=" + data[1];
          //     window.open(newUrl,'_blank')

          // });
          vm.table.on('order.dt search.dt', function() {
            vm.table.column(0, {
              search: 'applied',
              order: 'applied'
            }).nodes().each(function(cell, i) {
              cell.innerHTML = i + 1;
            });
          }).draw();

        });
      },
      appendTable(dataoutlet, aje, w) {
        for (var key in vm.inputs) {
          if (vm.inputs.hasOwnProperty(key)) {
            if (key == "offsetId") {
              console.log("offsetId -->", vm.dataPayload[key])
              if (vm.dataPayload[key] == undefined || vm.dataPayload[key] == 0) {
                vm.dataPayload[key] = vm.inputs[key];
              }
            } else {
              vm.dataPayload[key] = vm.inputs[key];
            }
          }
        }

        vm.dataPayload.shift = this.inputs.shift.join("','")
        vm.dataPayload.outlet = this.inputs.outlet.join("','")
        if ("<?= $promotion_id ?>" != "") {
          vm.dataPayload.promotion_id = "<?= $promotion_id ?>"
          // if (vm.isUsingDates === false) {
          vm.dataPayload.startDate = ""
          vm.dataPayload.endDate = ""
          vm.dataPayload.timeZone = ""
          // }
        }

        if ("<?= $customer ?>" != "") {
          vm.dataPayload.customer = "<?= $customer ?>"
          vm.dataPayload.contact = "<?= $contact ?>"
          // if (vm.isUsingDates === false) {
          vm.dataPayload.startDate = ""
          vm.dataPayload.endDate = ""
          // }
        }

        $.ajax({
          url: '<?= $ajaxActionDatatableAll ?>',
          type: 'POST',
          headers: {
            "Authorization": uniq.report.token,
          },
          data: vm.dataPayload,
          success(response) {
            vm.dataPayload.offsetId = response.offsetId
            var data = [];

            $.each(response.data, function() {
              var payment = []
              var method = []
              $.each(this.pembayaran, function(index, val) {
                payment.push(index, val)
                method.push(index)
              });

              var temp = payment.toString().replace(/(.+?,.+?),\s*/g, '$1\n');
              var output =
                '<span data-toggle="tooltip" data-placement="left" title="" data-original-title="' + temp
                .replace(/,/g, ' : ') + '">' + method + '</span>'

              //modify promotion info
              var promoInfoShort = this.keterangan_promo != null && this.keterangan_promo.length >= 15 ?
                this.keterangan_promo.substring(0, 15) : ""
              var promoInfoOutput =
                '<span data-toggle="tooltip" data-placement="left" title="" data-original-title="' + this
                .keterangan_promo + '">' + promoInfoShort + '</span>'

              // vm.table.row.add
              data.push([
                vm.no++,
                this.nomor_nota,
                this.outlet_name,
                this.shift_name,
                this.tanggal,
                this.status === "Refund" && this.refund ?
                '<span data-toggle="tooltip" data-placement="left" title="" data-original-title="Date: ' + this.refund.date +
                '\nEmployee: ' + this.refund.employee +
                '\nReason: ' + this.refund.reason + '">' + this.status + '</span>' :
                this.status,
                output,
                currency(this.pembayaran['CASH'] || 0),
                currency(Object.entries(this.pembayaran).reduce((total, [key, value]) => {
                  if (!['CASH', 'PIUTANG', 'DUTY MEALS', 'COMPLIMENT'].includes(key)) {
                    return total + value;
                  }
                  return total;
                }, 0)),
                currency(this.pembayaran['PIUTANG'] || 0),
                currency(this.pembayaran['DUTY MEALS'] || 0),
                currency(this.pembayaran['COMPLIMENT'] || 0),
                number(this.pax),
                this.employee,
                currency(this.sub_total),
                currency(this.total_discount),
                this.keterangan_discount,
                currency(this.total_voucher),
                this.keterangan_voucherS,
                currency(this.promo),
                promoInfoOutput,
                currency(this.tax),
                currency(this.service),
                this.order_type ?? '',
                this.customer_name ?
                (this.member_fkid ?
                  '<a href="<?= getenv("APP_URL") ?>/crm/member/lists?modal=open&member=' + this.member_fkid + '" target="_blank" style="color: #007bff; text-decoration: none;">' + this.customer_name + '</a>' :
                  this.customer_name) :
                '-',
                currency(this.grand_total),
                `<div class="dropdown">
                  <button class="btn btn-info btn-sm dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    <span class="glyphicon glyphicon-option-vertical" aria-hidden="true"></span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenu1">
                    <li><a href="#" class="btn-list-print"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print Receipt</a></li>
                  </ul>
                </div>`
              ]);

              var toarray = []

              function getoutlets() {
                for (var i = 0; i < response.data.length; i++) {
                  toarray.push(response.data[i].outlet_name);
                }
                return toarray;
              }
            }); //each end

            if (data.length > 0) {
              if (vm.isCancel === false) {
                vm.table.rows.add(data).draw()
                vm.inputs.offsetId = response.offset_id;
              }
            }

            setTimeout(function() {
              vm.table.columns.adjust().draw();
              console.log("ajust draw")
            }, 100);

            if (vm.isCancel) {
              vm.control(0)
            } else {
              vm.control(response.data.length)
            }
            isAiDataSent = false;
          },
          error() {
            Alert.error('Error', "silahkan Coba Lagi");
            loading_hide();
            vm.showCancelBtn = false
          },

        })
      },


      exportAllCurrent() {
        const date = new Date().toLocaleDateString("es-CL");
        const customdate = date.replace(/-|-|-/gi, "")
        const h = new Date().toLocaleString("en-US", {
          hour: '2-digit',
          hour12: false
        });
        const m = new Date().toLocaleTimeString("en-US", {
          minute: '2-digit'
        });
        const formatTime = h + m;
        const title = "SalesHistoryTransaksi";

        // Get outlet information
        let outletInfo;
        if (this.inputs.outlet.length === 0) {
          // If no outlet selected, means all outlets
          const totalOutlets = $('#select_outlet_all option').length;
          outletInfo = totalOutlets > 1 ? `${totalOutlets}Outlets` : "AllOutlet";
        } else if (this.inputs.outlet.length === 1) {
          // If single outlet, get outlet name
          const outletName = $('#select_outlet_all option[value="' + this.inputs.outlet[0] + '"]').text();
          outletInfo = outletName.replace(/\s+/g, ''); // Remove spaces from outlet name
        } else {
          // Multiple outlets selected
          outletInfo = `${this.inputs.outlet.length}Outlets`;
        }

        const result = `${title}_${outletInfo}_${customdate}_${formatTime}`;
        return result;
      },


      cancelOperation() {
        console.log("cancel")
        vm.isCancel = true
      },
      control(dataLength) {
        if (dataLength !== 0) {
          this.inputs.offset = this.inputs.limit + this.inputs.offset
          this.inputs.page = this.inputs.page + 1
          this.appendTable()
        } else if (vm.isCancel) {
          loading.hide();
          vm.showCancelBtn = false
          this.inputs.offset = 0
          this.inputs.page = 0
          this.inputs.offsetId = 0
          vm.no = 1
          $.notify("Load data di batalkan", "info");
        } else {
          loading.hide();
          vm.showCancelBtn = false
          this.inputs.offset = 0
          this.inputs.page = 0
          this.inputs.offsetId = 0
          vm.no = 1
          $.notify("Berhasil Menampilkan Semua Data", "success");
        }
      },
      resetState() {
        // $('#table-transaksi').clear().draw();
        this.table.clear().draw();
        this.no = 1
        this.inputs.limit = 50
        this.inputs.offset = 0
        this.inputs.page = 0
      },
      saveFiltersToCache() {
        const filters = {
          outlet: this.inputs.outlet,
          shift: this.inputs.shift,
          dataType: this.inputs.dataType,
          dataStatus: this.inputs.dataStatus,
          startDate: this.inputs.startDate,
          endDate: this.inputs.endDate
        };
        localStorage.setItem('salesHistoryFilters', JSON.stringify(filters));
      },

      loadFiltersFromCache() {
        const cached = localStorage.getItem('salesHistoryFilters');
        if (cached) {
          const filters = JSON.parse(cached);
          this.isLoadingFromCache = true;
          this.inputs.outlet = filters.outlet;
          this.inputs.shift = filters.shift;
          this.inputs.dataType = filters.dataType;
          this.inputs.dataStatus = filters.dataStatus;
          // this.inputs.startDate = filters.startDate;
          // this.inputs.endDate = filters.endDate;

          // Update the selectpicker UI
          this.$nextTick(() => {
            $('.outletSelect').selectpicker('val', filters.outlet);
            $('.shiftSelect').selectpicker('val', filters.shift);
          });
        }
      },
    },
    mounted() {
      $(function() {
        setTimeout(function() {
          vm.dataTable()
          vm.inputs.startDate = $('#date-all').data('daterangepicker').startDate._d.valueOf()
          vm.inputs.endDate = $('#date-all').data('daterangepicker').endDate._d.valueOf()
          vm.inputs.timeZone = timezone();

          // Load cached filters but don't auto-apply
          vm.loadFiltersFromCache();

          // Only auto-apply if we have promotion_id or customer filters
          if ("<?= $promotion_id ?>" != "" || "<?= $customer ?>" != "") {
            vm.showCancelBtn = true;
            loading.show();
            vm.appendTable();
          }
        }, 100);
      });
    }
  })

  function removeSpecialCharsKeepCase(text) {
    return text
      .replace(/[^a-zA-Z0-9\s]/g, ''); // Pertahankan huruf (besar/kecil), angka, spasi
  }

  $(function() {
    $('#date-all').on('apply.daterangepicker', function(ev, val) {
      $(this).children('span').html(val.startDate.format("D MMMM YYYY") + ' - ' + val.endDate.format(
        "D MMMM YYYY"));
      vm.inputs.startDate = val.startDate.valueOf()
      vm.inputs.endDate = val.endDate.valueOf()
    });
  });

  function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  $("#table-transaksi").on("click", ".btn-list-print", function(e) {
    loading_show()
    e.preventDefault();

    var table = $("#table-transaksi").DataTable(); // Ambil instance DataTables
    var row = $(this).closest("tr"); // Ambil baris yang diklik
    var data = table.row(row).data(); // Ambil data dari baris tersebut

    const encodedOutlet = encodeURIComponent(data[2])

    const url = `<?= base_url() ?>reports/sales/history/generate_nota_print?display_nota=${data[1]}&outlet=${encodedOutlet}&start_date=${vm.inputs.startDate}&end_date=${vm.inputs.endDate}`;

    // fetch(url).then((res) => res.json()).then((data) => {
    //   console.log(data);
    // })

    fetch(url, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
        },
      })
      .then((res) => res.blob())
      .then((blob) => {
        loading_hide();

        $("#pdfModal").modal("show");

        // Render pakai pdf.js
        if (isMobile()) {
          document.getElementById("pdf-frame").style.display = "none";
          document.getElementById("pdfFrame").style.display = "block";
          renderPDFWithBlob(blob);
        } else {
          document.getElementById("pdfFrame").style.display = "none";
          document.getElementById("pdf-frame").style.display = "block";
          const url = window.URL.createObjectURL(blob);
          document.getElementById("pdf-frame").src = url;
        }

        // Tombol print
        $("#btn-print").off("click").on("click", function() {
          const blobUrl = window.URL.createObjectURL(blob);
          const printWindow = window.open(blobUrl, "_blank");
          printWindow.onload = function() {
            printWindow.print();
          };
        });

        // Tombol download (optional)
        // $("#btn-download").off("click").on("click", function () {
        //   const a = document.createElement("a");
        //   a.href = window.URL.createObjectURL(blob);
        //   a.download = "nota_pembelian.pdf";
        //   document.body.appendChild(a);
        //   a.click();
        //   document.body.removeChild(a);
        // });

        setTimeout(() => window.URL.revokeObjectURL(blob), 10000);
      })
      .catch((error) => console.error("Gagal membuka PDF:", error));
  })

  function createMarkdownTable(structuredData) {
    if (!structuredData || structuredData.length === 0) {
      return "No data available";
    }

    // Get number of rows to display (up to 3)
    const numRows = Math.min(3, structuredData.length);

    // Get headers from the first object's keys
    const headers = Object.keys(structuredData[0]);

    // Create header row with aligned separators
    const headerRow = '| ' + headers.join(' | ') + ' |';
    const separator = '|' + headers.map(() => '---').join('|') + '|';

    // Create data rows
    const dataRows = structuredData
      .slice(0, numRows)
      .map(row => '| ' + headers.map(header => row[header] || '').join(' | ') + ' |');

    // Combine all parts
    return [headerRow, separator, ...dataRows].join('\n');
  }

  function stripHtml(data) {
    var temp = document.createElement('div');
    temp.innerHTML = data;
    // Get just the text content, removing HTML
    dataClean = temp.textContent || temp.innerText || '';
    // Trim any extra whitespace
    return dataClean.trim();
  }

  // Custom Column Search Panel Functionality
  $(function() {
    // Column definitions for search
    const columns = [{
        index: 1,
        title: 'No Order'
      },
      {
        index: 2,
        title: 'Outlet'
      },
      {
        index: 3,
        title: 'Shift'
      },
      {
        index: 4,
        title: 'Date'
      },
      {
        index: 5,
        title: 'Status'
      },
      {
        index: 6,
        title: 'Payment'
      },
      {
        index: 7,
        title: 'Cash'
      },
      {
        index: 8,
        title: 'Card'
      },
      {
        index: 9,
        title: 'Piutang'
      },
      {
        index: 10,
        title: 'Duty Meals'
      },
      {
        index: 11,
        title: 'Compliment'
      },
      {
        index: 12,
        title: 'PAX'
      },
      {
        index: 13,
        title: 'Operator'
      },
      {
        index: 14,
        title: 'Sub Total'
      },
      {
        index: 15,
        title: 'Discount total'
      },
      {
        index: 16,
        title: 'Discount Info.'
      },
      {
        index: 17,
        title: 'Voucher'
      },
      {
        index: 18,
        title: 'Voucher Info.'
      },
      {
        index: 19,
        title: 'Promo'
      },
      {
        index: 20,
        title: 'Promo Info.'
      },
      {
        index: 21,
        title: 'TAX'
      },
      {
        index: 22,
        title: 'Service'
      },
      {
        index: 23,
        title: 'Order Type'
      },
      {
        index: 24,
        title: 'Customer'
      },
      {
        index: 25,
        title: 'Grand Total'
      }
    ];

    // Function to create a search field for a column
    function createSearchField(column) {
      return `
        <div class="form-group" data-column="${column.index}">
          <label>${column.title}</label>
          <input type="text" class="form-control column-search" data-column="${column.index}" placeholder="Search ${column.title}...">
        </div>
      `;
    }

    // Initialize default search fields (first 5 columns)
    function initializeDefaultSearchFields() {
      $('#defaultSearchFields').empty();
      for (let i = 0; i < 5; i++) {
        $('#defaultSearchFields').append(createSearchField(columns[i]));
      }
    }

    // Create dropdown for selecting additional columns
    function createColumnSelector() {
      let options = '';
      // Skip the first 5 columns as they're already in the default section
      for (let i = 5; i < columns.length; i++) {
        options += `<option value="${columns[i].index}">${columns[i].title}</option>`;
      }

      return `
        <select class="form-control column-select" id="columnSelector">
          <option value="">Select a column...</option>
          ${options}
        </select>
      `;
    }

    // Add column selector
    $('#addSearchField').on('click', function() {
      // Remove existing selector if present
      $('#columnSelector').remove();

      // Add the column selector before the first item in additionalSearchFields
      $('#additionalSearchFields').prepend(createColumnSelector());

      // Handle selection change
      $('#columnSelector').on('change', function() {
        const columnIndex = $(this).val();
        if (columnIndex) {
          // Find the column object
          const column = columns.find(col => col.index == columnIndex);
          if (column) {
            // Check if this column is already added
            if ($(`#additionalSearchFields .form-group[data-column="${columnIndex}"]`).length === 0) {
              // Add search field for this column
              $('#additionalSearchFields').append(createSearchField(column));
              // Remove this option from selector and reset selector
              $(this).find(`option[value="${columnIndex}"]`).remove();
              $(this).val('');
            }
          }
        }
      });
    });

    // Close panel
    $('#closeSearchPanel').on('click', function() {
      $('#searchPanel').removeClass('active');
    });

    // Apply search
    $('#applySearch').on('click', function() {
      let table = $('#table-transaksi').DataTable();

      // Clear all filters first
      table.columns().search('').draw();

      // Apply search to each column with input
      $('.column-search').each(function() {
        const columnIndex = $(this).data('column');
        const searchTerm = $(this).val();

        if (searchTerm) {
          table.column(columnIndex).search(searchTerm);
        }
      });

      // Draw the table with the new search filters
      table.draw();
    });

    // Clear search
    $('#clearSearch').on('click', function() {
      // Clear all input fields
      $('.column-search').val('');

      // Clear all filters and redraw table
      let table = $('#table-transaksi').DataTable();
      table.columns().search('').draw();
    });

    // Initialize the panel after table is ready
    const initSearchPanel = function() {
      initializeDefaultSearchFields();
    };

    // Initialize search panel after datatable is ready
    setTimeout(function() {
      initSearchPanel();
    }, 1500);
  });
</script>