<style type="text/css">
	/* Ensure that the demo table scrolls */
	th,
	td {
		white-space: nowrap;
	}

	div.dataTables_wrapper {
		margin: 0 auto;
	}

	div.dt-buttons {
		float: right;
		margin-left: 10px;
	}
</style>
<div class="container-fluid">
	<div class="row">
		<div class="col-sm-4">
			<div>
				<h3>Report <small>COGS</small></h3>
			</div>
		</div>
		<div class="col-sm-8">
			<div class="pull-right" style="text-align: right;">
				<div class="btn-group label-tabele">
					<button type="button" class=" btn btn-info" id="dateProduct">
						<span>
							<i class="glyphicon glyphicon-calendar"></i> Date
						</span>
						<i class="caret"></i>
					</button>
				</div>
				<div class="btn-group label-tabele">
					<select class="form-control btn btn-info" id="select_outlet" name="select_outlet">
						<option value="0" selected="">ALL Outlet</option>
						<?php foreach ($form_select_outlet as $a) : ?>
							<option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
						<?php endforeach ?>
					</select>
				</div>
				<div class="btn-group label-tabele">
					<select class="form-control btn btn-info" id="select_category" name="select_category">
						<option value="0" selected="">ALL category</option>
						<?php foreach ($from_select_category as $a) : ?>
							<option value="<?= $a->product_category_id ?>"><?= htmlentities($a->name) ?></option>
						<?php endforeach ?>
					</select>
				</div>
				<div class="btn-group label-tabele">
					<button class="btn btn-primary btn-block" id="btnProduct" type="button">Apply</button>
				</div>
			</div>
		</div>
	</div>
	<!--content-->
	<div class="row">
		<div class="col-sm-12">
			<div class="content-uniq table-responsive">
				<table id="tableProduct" class="table table-striped table-report" cellspacing="0" width="100%">
					<thead>
						<tr>
							<th>NO</th>
							<th>Outlet</th>
							<th>SKU</th>
							<th>Category</th>
							<th>Sub Category</th>
							<th>Product</th>
							<th>Qty Sales</th>
							<th>AVG Price</th>
							<th>Qty Purchase</th>
							<th>AVG Hpp</th>
							<th>Disc & Voucher</th>
							<th>Tax</th>
							<th>Promo</th>
							<th>Total Hpp</th>
							<th>Sub Total</th>
							<th>Profit</th>
							<th>#</th>
						</tr>
					</thead>
					<tfoot>
						<tr>
							<th>Total</th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
						</tr>
						<tr>
							<th>Total Halaman</th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
							<th></th>
						</tr>
					</tfoot>
				</table>
			</div>
		</div>
	</div>

	<!-- =================== Modal Detail =================== -->
	<div class="modal fade" id="modal-sales-cogs">
		<div class="modal-dialog modal-lg">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">Detail Const of Goods Sold</h4>
				</div>

				<div class="modal-body">
					<div class="row">
						<div class="col-lg-12">
							<div class="table-responsive">
								<table class="table table-report" id="table-cogs-detail">
									<thead>
										<tr>
											<th>No</th>
											<th>Poduct Name</th>
											<th>Order Id</th>
											<th>Date</th>
											<th>Qty</th>
											<th>HPP</th>
											<th>Price</th>
											<th>Disc & voucher</th>
											<th>Tax</th>
											<th>Promo</th>
											<th>Subtotal</th>
											<th>Total HPP</th>
											<th>Profit</th>
										</tr>
									</thead>
									<tfoot>
										<tr>
											<th>Total</th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
										</tr>
										<tr>
											<th>Total Halaman</th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
											<th></th>
										</tr>
									</tfoot>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- =================== Modal Detail =================== -->
</div>


<script type="text/javascript">
	$(document).ready(function() {
		// Initialize variables
		let tableCogsDetail = null;
		var d = new Date();
		var e = d.getTimezoneOffset();
		var timeZone = e * 60 * -1;
		var start = moment().subtract(30, 'days');
		var end = moment();
		var outlet = $("#select_outlet").val();
		var shift = 0;
		var productCategory = 0;
		var startDate = start.format('YYYY-MM-DD');
		var endDate = end.format('YYYY-MM-DD');
		var mainTable = null;
		var baseUrl = '<?= base_url() ?>';

		// Add format helpers
		var numFormat = $.fn.dataTable.render.number(formatAngka(), ).display;
		var formatDes = $.fn.dataTable.render.number('\.', ',', 2, ).display;

		// Initialize date picker display
		$('#dateProduct span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));

		// Initialize empty table
		initializeEmptyTable();

		// Apply button click handler
		$('#btnProduct').on('click', function() {
			console.log('Apply button clicked');
			loadData();
		});

		function initializeEmptyTable() {
			mainTable = $('#tableProduct').DataTable({
				dom: "<'row'<'col-sm-6'l><'col-sm-6'Bf>>" +
					"<'row'<'col-sm-12'tr>>" +
					"<'row'<'col-sm-5'i><'col-sm-7'p>>",
				scrollY: 'auto',
				scrollX: true,
				buttons: [{
					extend: 'collection',
					text: 'EXPORT',
					buttons: [{
						extend: 'pdfHtml5',
						orientation: 'landscape',
						pageSize: 'legal',
						title: "REPORT COGS",
						filename: detail(),
						customize: function(doc) {
							doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
						},
						exportOptions: {
							columns: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15]
						}
					},
					{
						extend: 'excelHtml5',
						orientation: 'landscape',
						pageSize: 'legal',
						title: "REPORT COGS",
						filename: detail(),
						exportOptions: {
							columns: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15],
							format: {
								body: function(data, row, column, node) {
									data = $('<p>' + data + '</p>').text();
									let symbl = '<?php echo $this->session->userdata('currency_symbol') ?>';
									if ((data.startsWith(symbl) || data.startsWith('-' + symbl)) && $.isNumeric(data.replace(/[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, ''))) {
										data = data.replace(/(,|\.)[0-9]{2}$/g, "");
										return data.replace(/[^0-9-]/g, "");
									}
									return data;
								}
							}
						}
					}]
				}],
				columnDefs: [{
					type: 'formatted-num',
					targets: [6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
				},
				{
					"targets": [8],
					"visible": true
				}],
				data: [], // Initialize with empty data
				columns: [{
						"data": null
					},
					{
						"data": "outlet"
					},
					{
						"data": "sku"
					},
					{
						"data": "category"
					},
					{
						"data": "sub_category"
					},
					{
						"data": "nama_produk"
					},
					{
						"data": "qty_sales"
					},
					{
						"data": "harga_jual",
						render: function(data, type, row) {
							return currency(row.harga_jual);
						}
					},
					{
						"data": "qty_purchase"
					},
					{
						"data": "hpp",
						render: function(data, type, row) {
							return currency(row.hpp);
						}
					},
					{
						"data": "disc",
						render: function(data, type, row) {
							return currency(row.disc);
						}
					},
					{
						"data": "tax",
						render: function(data, type, row) {
							return currency(row.tax);
						}
					},
					{
						"data": "promo",
						render: function(data, type, row) {
							return currency(row.promo);
						}
					},
					{
						"data": "total_hpp",
						render: function(data, type, row) {
							return currency(row.total_hpp);
						}
					},
					{
						"data": "sub_total",
						render: function(data, type, row) {
							return currency(row.sub_total);
						}
					},
					{
						render: function(data, type, row) {
							let profit = row.sub_total - row.total_hpp;
							return currency(profit);
						}
					},
					{
						render: function(data, type, row) {
							return `<button class="btn btn-warning btn-xs btn-detail" data-pd-id="${row.pd_id}"><i class="fa fa-th-list" aria-hidden="true"></i></button>`;
						}
					}
				],
				language: {
					emptyTable: "Please select filters and click Apply to load data",
					processing: "Loading data, please wait...",
					zeroRecords: "No matching records found"
				},
				processing: true,
				serverSide: false,
				scrollY: "440px",
				scrollCollapse: true,
				paging: true,
				ordering: true
			});

			// Add numbering to rows
			mainTable.on('order.dt search.dt', function() {
				mainTable.column(0, {
					search: 'applied',
					order: 'applied'
				}).nodes().each(function(cell, i) {
					cell.innerHTML = i + 1;
				});
			}).draw();

			return mainTable;
		}

		function loadData() {
			loading_show();
			
			// Get current filter values
			outlet = $("#select_outlet").val();
			productCategory = $("#select_category").val();
			
			// Use the current date values
			startDate = start.format('YYYY-MM-DD');
			endDate = end.format('YYYY-MM-DD');

			console.log('Loading data with filters:', {
				startDate,
				endDate,
				timeZone,
				productCategory,
				outlet
			});

			// Verify parameters
			if (!startDate || !endDate) {
				Alert.error('Error', 'Invalid date range');
				loading_hide();
				return;
			}

			// Build the URL with parameters
			const url = `${baseUrl}reports/sales/report_cogs/get_data_v3/${startDate}/${endDate}/${timeZone}/${productCategory}/${outlet}`;
			console.log('Request URL:', url);

			// Clear existing table
			if (mainTable) {
				mainTable.destroy();
			}
			$("#tableProduct > tbody > tr").remove();

			// Make the AJAX request manually first
			$.ajax({
				url: url,
				type: 'POST',
				timeout: 30000,
				beforeSend: function() {
					console.log('AJAX Request starting:', url);
				},
				success: function(response) {
					console.log('AJAX Request successful');
					
					// Initialize DataTable with received data
					mainTable = $("#tableProduct").DataTable({
						dom: "<'row'<'col-sm-6'l><'col-sm-6'Bf>>" +
							"<'row'<'col-sm-12'tr>>" +
							"<'row'<'col-sm-5'i><'col-sm-7'p>>",
						data: response.data || [],
						scrollY: 'auto',
						scrollX: true,
						processing: true,
						serverSide: false,
						buttons: [{
							extend: 'collection',
							text: 'EXPORT',
							buttons: [{
								extend: 'pdfHtml5',
								orientation: 'landscape',
								pageSize: 'legal',
								title: "REPORT COGS",
								filename: detail(),
								customize: function(doc) {
									doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
								},
								exportOptions: {
									columns: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15]
								}
							},
							{
								extend: 'excelHtml5',
								orientation: 'landscape',
								pageSize: 'legal',
								title: "REPORT COGS",
								filename: detail(),
								exportOptions: {
									columns: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15],
									format: {
										body: function(data, row, column, node) {
											data = $('<p>' + data + '</p>').text();
											let symbl = '<?php echo $this->session->userdata('currency_symbol') ?>';
											if ((data.startsWith(symbl) || data.startsWith('-' + symbl)) && $.isNumeric(data.replace(/[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, ''))) {
												data = data.replace(/(,|\.)[0-9]{2}$/g, "");
												return data.replace(/[^0-9-]/g, "");
											}
											return data;
										}
									}
								}
							}]
						}],
						columnDefs: [{
							type: 'formatted-num',
							targets: [6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
						},
						{
							"targets": [8],
							"visible": true
						}],
						columns: [{
								"data": null
							},
							{
								"data": "outlet"
							},
							{
								"data": "sku"
							},
							{
								"data": "category"
							},
							{
								"data": "sub_category"
							},
							{
								"data": "nama_produk"
							},
							{
								"data": "qty_sales"
							},
							{
								"data": "harga_jual",
								render: function(data, type, row) {
									return currency(row.harga_jual);
								}
							},
							{
								"data": "qty_purchase"
							},
							{
								"data": "hpp",
								render: function(data, type, row) {
									return currency(row.hpp);
								}
							},
							{
								"data": "disc",
								render: function(data, type, row) {
									return currency(row.disc);
								}
							},
							{
								"data": "tax",
								render: function(data, type, row) {
									return currency(row.tax);
								}
							},
							{
								"data": "promo",
								render: function(data, type, row) {
									return currency(row.promo);
								}
							},
							{
								"data": "total_hpp",
								render: function(data, type, row) {
									return currency(row.total_hpp);
								}
							},
							{
								"data": "sub_total",
								render: function(data, type, row) {
									return currency(row.sub_total);
								}
							},
							{
								render: function(data, type, row) {
									let profit = row.sub_total - row.total_hpp;
									return currency(profit);
								}
							},
							{
								render: function(data, type, row) {
									return `<button class="btn btn-warning btn-xs btn-detail" data-pd-id="${row.pd_id}"><i class="fa fa-th-list" aria-hidden="true"></i></button>`;
								}
							}
						],
						language: {
							emptyTable: "No data available",
							processing: "Loading data, please wait...",
							zeroRecords: "No matching records found"
						},
						"footerCallback": function(row, data, start, end, display) {
							var api = this.api(),
							data;

							// Remove the formatting to get integer data
							var intVal = function(i) {
								return typeof i === 'string' ?
									i.replace(/[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '') * 1 :
									typeof i === 'number' ?
									i : 0;
							};

							// Total over all pages
							qty = api.column(6, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);

							price = api.column(7, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							qty_purchase = api.column(8, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							hpp = api.column(9, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							disc = api.column(10, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							tax = api.column(11, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							promo = api.column(12, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							totalHpp = api.column(13, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							subTotal = api.column(14, {
								page: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);


							qty1 = api.column(6, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							price1 = api.column(7, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							qty_purchase1 = api.column(8, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							hpp1 = api.column(9, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							disc1 = api.column(10, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							tax1 = api.column(11, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							promo1 = api.column(12, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							totalHpp1 = api.column(13, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);
							subTotal1 = api.column(14, {
								total: 'current'
							}).data().reduce(function(a, b) {
								return intVal(a) + intVal(b);
							}, 0);

							$('tr:eq(1) th:eq(6)', api.table().footer()).html(number(qty1));
							$('tr:eq(1) th:eq(7)', api.table().footer()).html(currency(price1));
							$('tr:eq(1) th:eq(8)', api.table().footer()).html(numFormat(qty_purchase1));
							$('tr:eq(1) th:eq(9)', api.table().footer()).html(currency(hpp1));
							$('tr:eq(1) th:eq(10)', api.table().footer()).html(currency(disc1));
							$('tr:eq(1) th:eq(11)', api.table().footer()).html(currency(tax1));
							$('tr:eq(1) th:eq(12)', api.table().footer()).html(currency(promo1));
							$('tr:eq(1) th:eq(13)', api.table().footer()).html(currency(totalHpp1));
							$('tr:eq(1) th:eq(14)', api.table().footer()).html(currency(subTotal1));
							$('tr:eq(1) th:eq(15)', api.table().footer()).html(currency(subTotal1 - totalHpp1));

							// Update footer
							$(api.column(6).footer()).html(number(qty));
							$(api.column(7).footer()).html(currency(price));
							$(api.column(8).footer()).html(numFormat(qty_purchase));
							$(api.column(9).footer()).html(currency(hpp));
							$(api.column(10).footer()).html(currency(disc));
							$(api.column(11).footer()).html(currency(tax));
							$(api.column(12).footer()).html(currency(promo));
							$(api.column(13).footer()).html(currency(totalHpp));
							$(api.column(14).footer()).html(currency(subTotal));
							$(api.column(15).footer()).html(currency(subTotal - totalHpp));
						}
					});

					// Add detail button click handler
					$("#tableProduct").off('click', '.btn-detail').on('click', '.btn-detail', function() {
						let data_row = mainTable.row($(this).closest("tr")).data();
						let url = "<?= $url_detail ?>";
						let formData = new FormData();
						formData.append("product_detail_id", data_row.pd_id);
						formData.append("timeZone", timeZone);
						formData.append("startDate", startDate);
						formData.append("endDate", endDate);

						$("#modal-sales-cogs").off('shown.bs.modal').on("shown.bs.modal", function() {
							if (tableCogsDetail != null) {
								tableCogsDetail.clear().draw();
								tableCogsDetail.destroy();
								tableCogsDetail = null;
							}

							tableCogsDetail = $("#table-cogs-detail").DataTable({
								serverSide: false,
								scrollX: true,
								destroy: true,
								paging: true,
								fixedColumns: {
									leftColumns: 3,
								},
								columns: [{
									data: null
								}, {
									data: "product_name"
								}, {
									data: "display_nota",
									render: function(data, type, row) {
										return `<a href="<?= base_url('reports/sales/history?tab=detail&id=') ?>${data}" target="_blank">${data}</a>`;
									}
								}, {
									data: "time_created"
								}, {
									data: "qty",
								}, {
									data: "hpp",
									render: function(data, type, row) {
										return currency(data);
									}
								}, {
									data: "price",
									render: function(data, type, row) {
										return currency(data);
									}
								}, {
									data: "disc",
									render: function(data, type, row) {
										return currency(data);
									}
								}, {
									data: "tax",
									render: function(data, type, row) {
										return currency(data);
									}
								}, {
									data: "promo",
									render: function(data, type, row) {
										return currency(data);
									}
								}, {
									data: "sub_total",
									render: function(data, type, row) {
										return currency(data);
									}
								}, {
									data: "total_hpp",
									render: function(data, type, row) {
										return currency(data);
									}
								}, {
									data: "profit",
									render: function(data, type, row) {
										return currency(data);
									}
								}],
								"footerCallback": function(row, data, start, end, display) {
									var api = this.api();
									var data;

									let intVal = function(i) {
										return typeof i === 'string' ? i.replace(/[<?= $this->session->userdata('currency_symbol') ?>.,]/g, '') * 1 : typeof i === 'number' ? i : 0;
									};

									let qty = api.column(4, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(4).footer()).html(number(qty));
									let qty1 = api.column(4, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(4)', api.table().footer()).html(number(qty1));

									let hpp = api.column(5, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(5).footer()).html(currency(hpp));
									let hpp1 = api.column(5, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(5)', api.table().footer()).html(currency(hpp1));

									let price = api.column(6, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(6).footer()).html(currency(price));
									let price1 = api.column(6, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(6)', api.table().footer()).html(currency(price1));

									let disc = api.column(7, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(7).footer()).html(currency(disc));
									let disc1 = api.column(7, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(7)', api.table().footer()).html(currency(disc1));

									let tax = api.column(8, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(8).footer()).html(currency(tax));
									let tax1 = api.column(8, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(8)', api.table().footer()).html(currency(tax1));

									let promo = api.column(9, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(9).footer()).html(currency(promo));
									let promo1 = api.column(9, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(9)', api.table().footer()).html(currency(promo1));

									let subtotal = api.column(10, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(10).footer()).html(currency(subtotal));
									let subtotal1 = api.column(10, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(10)', api.table().footer()).html(currency(subtotal1));

									let totalhpp = api.column(11, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(11).footer()).html(currency(totalhpp));
									let totalhpp1 = api.column(11, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(11)', api.table().footer()).html(currency(totalhpp1));

									let profit = api.column(12, {
										page: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$(api.column(12).footer()).html(currency(profit));
									let profit1 = api.column(12, {
										total: "current"
									}).data().reduce(function(a, b) {
										return intVal(a) + intVal(b);
									}, 0);
									$('tr:eq(1) th:eq(12)', api.table().footer()).html(currency(profit1));
								}
							});

							// Add numbering to detail table rows
							tableCogsDetail.on('order.dt search.dt', function() {
								tableCogsDetail.column(0, {
									search: 'applied',
									order: 'applied'
								}).nodes().each(function(cell, i) {
									cell.innerHTML = i + 1;
								});
							}).draw();
						});

						loading.show();
						fetch(url, {
							method: "POST",
							body: formData,
							headers: {
								"X-Requested-With": "XMLHttpRequest",
							}
						}).then((res) => res.json())
						.then((data) => {
							loading.hide();
							$("#modal-sales-cogs").modal('show');
							setTimeout(() => {
								tableCogsDetail.rows.add(data).draw();
							}, 1000);
						}).catch((error) => {
							loading.hide();
							console.error('Error loading details:', error);
							Alert.error('Error', 'Failed to load transaction details');
						});
					});

					// Add numbering to rows
					mainTable.on('order.dt search.dt', function() {
						mainTable.column(0, {
							search: 'applied',
							order: 'applied'
						}).nodes().each(function(cell, i) {
							cell.innerHTML = i + 1;
						});
					}).draw();

					loading_hide();
				},
				error: function(xhr, status, error) {
					console.log('AJAX Error:', {
						status: xhr.status,
						error: status,
						details: error
					});
					loading_hide();
					Alert.error('Error', status === 'timeout' ? 
						'Request timed out. Please try again.' : 
						'Failed to load data. Please try again.'
					);
					
					// Reinitialize empty table on error
					initializeEmptyTable();
				},
				complete: function() {
					console.log('AJAX Request completed');
					loading_hide();
				}
			});
		}

		function detail() {
			const date = new Date().toLocaleDateString("es-CL");
			const customdate = date.replace(/-|-|-/gi, "")
			const h = new Date().toLocaleString("en-US", {
				hour: '2-digit',
				hour12: false
			}); // 09 (just the hour)
			const m = new Date().toLocaleTimeString("en-US", {
				minute: '2-digit',
			});
			const formatTime = h + m;
			const title = "ReportCOGS";
			const content = "AllOutlet";

			const result = "" + title + "_" + "" + content + "" + "_" + customdate + "_" + formatTime + "";
			return result;
		}

		//select outlet start
		$('#select_outlet').focus(function() {
			outlet = $(this).val();
		}).bind('change', function() {
			if ($(this).val() == 0) {
				swal({
					title: 'Warning!',
					text: "Proses ini mungkin membutuhkan sedikit waktu, pastikan koneksi internet anda dalam keadaan baik",
					type: 'warning',
					showCancelButton: true,
					confirmButtonColor: '#3085d6',
					cancelButtonColor: '#d33',
					confirmButtonText: 'Lanjut'
				}).then((result) => {
					if (result.value) {
						outlet = $(this).val();
					} else {
						$(this).val(outlet);
						return false;
					}
				})
			} else {
				outlet = $(this).val();
			}
		});

		//select category start
		$('#select_category').on('change', function() {
			productCategory = $(this).val();
		});

		//select date start
		$('#dateProduct').daterangepicker({
			ranges: {
				'Today': [moment(), moment()],
				'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
				'Last 7 Days': [moment().subtract(6, 'days'), moment()],
				'Last 30 Days': [moment().subtract(29, 'days'), moment()],
				'This Month': [moment().startOf('month'), moment().endOf('month')],
				'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
			},
			startDate: start,
			endDate: end,
			opens: "left"
		},
		function(newStart, newEnd) {
			start = newStart;
			end = newEnd;
			$('#dateProduct span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
			startDate = start.format('YYYY-MM-DD');
			endDate = end.format('YYYY-MM-DD');
			console.log('Date changed:', { startDate, endDate });
		});
	});
</script>