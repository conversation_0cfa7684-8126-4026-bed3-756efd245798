<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Report_sales_history_model extends CI_Model
{

	public $table = 'sales_detail';
	public $id = 'sales_detail_id';
	public $order = 'DESC';

	function __construct()
	{
		parent::__construct();
		$this->load->model('outlet/Outlets_model', 'Outlets_model');
	}

	public function jsonTransaksi($parm)
	{
		$date_range = role_daterange("reports", $this->session->userdata('user_id'));

		$this->datatables->select("
        s.sales_id AS nomor_nota,
        s.display_nota AS display_nota,
        s.time_created AS tanggal,
        s.status AS status,

        s.qty_customers AS pax,
        SUM(s.discount) AS discountSales,
        SUM(sd.discount) AS discountSd,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax WHERE category = 'discount' AND sales_fkid = sd.sales_fkid),0) AS discountTax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax WHERE category = 'service' AND sales_fkid = sd.sales_fkid),0) AS service,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax WHERE category = 'tax' AND sales_fkid = sd.sales_fkid),0) AS tax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax WHERE category = 'voucher' AND sales_fkid = sd.sales_fkid),0) AS voucherTax,


        s.grand_total AS grand_total,
        s.voucher AS voucherSales,
        s.voucher_info AS keterangan_voucherS,
        s.discount_info AS keterangan_discountS,
        MIN(sd.discount_info) AS keterangan_discountSd,
        sum(sd.sub_total) AS sub_total,
        MIN(o.name) AS outlet_name,
        MIN(sf.name) AS shift_name,
        MIN(em.name) AS employee,
		COALESCE(GROUP_CONCAT(distinct p.name), GROUP_CONCAT(distinct p2.name))  AS promo_info,
		GROUP_CONCAT(distinct sp.voucher_code) as promo_voucher,
        s.time_created,
        (
            SELECT
                sum(sub_total)
            from
                sales_void sv
            where
                sv.sales_fkid = s.sales_id) as sub_void,
        sum((select sum(total) from sales_detail_discount where sales_detail_fkid = sd.sales_detail_id and type = 'discount')) as discount_sales,

        (ifnull((select SUM(promotion_value) from sales_promotion where sales_fkid = s.sales_id),0)+ifnull((select SUM(promotion_value) from sales_detail_promotion where sales_fkid=s.sales_id and sales_detail_fkid is not null),0)) as promo
        ");

		$this->db->from('sales s');
		$this->db->join('outlets o', 's.outlet_fkid = o.outlet_id', 'left');
		$this->db->join('employee em', 'em.employee_id = s.employee_fkid', 'left');
		$this->db->join('open_shift os', 's.open_shift_fkid = os.open_shift_id', 'left');
		$this->db->join('shift sf', 'os.shift_fkid = sf.shift_id', 'left');
		$this->db->join('sales_detail sd', 'sd.sales_fkid = s.sales_id', 'left');
		$this->db->join('sales_promotion sp', 'sp.sales_fkid = s.sales_id', 'left');
		$this->db->join('sales_detail_promotion sdp', 'sdp.sales_fkid = s.sales_id', 'left');
		$this->db->join('promotions p', 'p.promotion_id = sp.promotion_fkid', 'left');
		$this->db->join('promotions p2', 'p2.promotion_id = sdp.promotion_fkid', 'left');
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('s.data_status', 'on');
		$this->db->where('sf.shift_id in ' . $parm['shift']);
		$this->db->where('o.outlet_id in ' . $parm['outlet']);

		if ($parm["promotion_id"] != "") {
			$this->db->where("sales_id in 
			(SELECT sales_fkid from sales_detail_promotion sdp where promotion_fkid = " . $parm["promotion_id"] . "
			UNION 
			SELECT  sales_fkid  from sales_promotion sp  where promotion_fkid = " . $parm["promotion_id"] . " )
			");
		}

		if ($parm["contact"] != "") {
			$this->db->where("s.receipt_receiver", $parm["contact"]);
			$this->db->where("s.member_fkid", null);
		} else if ($parm["customer_name"] != "") {
			$this->db->where("s.customer_name", $parm["customer_name"]);
			$this->db->where("s.member_fkid", null);
			$this->db->group_start()
				->where('s.receipt_receiver', null)
				->or_where('s.receipt_receiver', '')
				->group_end();
			// $this->db->where("s.receipt_receiver IS NULL", NULL, FALSE);
		}

		switch ($parm['dataStatus']) {
			case 'success':
				$this->db->where('s.status', 'success');
				break;
			case 'refund':
				$this->db->where('s.status', 'refund');
				break;
		}

		if ($parm['startDate'] == '') {
			$parm['startDate'] = 1514653200000;
		}
		if ($parm['endDate'] == '') {
			$parm['endDate'] = round(microtime(true) * 1000);
		}

		if ($this->session->userdata('user_type') == "employee") {
			if (!empty($date_range) && $date_range > millis_to_localtime("Y-m-d", $parm['startDate'])) {
				if ($parm['dataType'] == '1') {
					$this->db->where("date_format(from_unixtime(((s.time_created / 1000 + " . $parm['timeZone'] . "))), '%Y-%m-%d') >=", $date_range);
					$this->db->where("date_format(from_unixtime(((s.time_created / 1000 + " . $parm['timeZone'] . "))), '%Y-%m-%d') <=", millis_to_localtime("Y-m-d", $parm['endDate']));
				} else {
					$this->db->where("date_format(from_unixtime(((os.time_open / 1000 + " . $parm['timeZone'] . "))), '%Y-%m-%d') >=", $date_range);
					$this->db->where("date_format(from_unixtime(((os.time_open / 1000 + " . $parm['timeZone'] . "))), '%Y-%m-%d') <=", millis_to_localtime("Y-m-d", $parm['endDate']));
				}
			} else {
				if ($parm['dataType'] == '1') {
					$this->db->where("s.time_created BETWEEN " . $parm['startDate'] . " AND " . $parm['endDate']);
				} else {
					$this->db->where("os.time_open BETWEEN " . $parm['startDate'] . " AND " . $parm['endDate']);
				}
			}
		} else {
			$this->db->where("s.time_created BETWEEN " . $parm['startDate'] . " AND " . $parm['endDate']);
		}

		if (isset($offsetId) && $parm['offset'] > 0) {
			$this->db->where('s.time_created > ', $parm['offset']);
			$this->db->limit($parm['limit']);
		} else {
			$this->db->limit($parm['limit'], $parm['offset']);
		}

		$this->db->group_by('s.sales_id');
		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", count($result) . " data, query: " . trim(preg_replace('/\s\s+/', ' ', $this->db->last_query())) . "\n");
		return $result;
	}

	public function jsonTransaksiV2($parm)
	{
		$date_range = role_daterange("reports", $this->session->userdata('user_id'));
		$this->datatables->select("
        s.sales_id AS nomor_nota,
        s.display_nota AS display_nota,
        s.time_created AS tanggal,
        s.status AS status,

        s.qty_customers AS pax,
        SUM(s.discount) AS discountSales,
        SUM(sd.discount) AS discountSd,

			 coalesce(min(stg.discountTax), 0)                                              AS discountTax,
       coalesce(min(stg.service), 0)                                                  AS service,
       coalesce(min(stg.tax), 0)                                                      AS tax,
       coalesce(min(stg.voucherTax), 0)                                               AS voucherTax,

        s.grand_total AS grand_total,
        s.voucher AS voucherSales,
        s.voucher_info AS keterangan_voucherS,
        s.discount_info AS keterangan_discountS,
        MIN(sd.discount_info) AS keterangan_discountSd,
        sum(sd.sub_total) AS sub_total,
        MIN(o.name) AS outlet_name,
        MIN(sf.name) AS shift_name,
        MIN(em.name) AS employee,
				sum(sv.sub_total)                                                              AS sub_void,
       sum(if(sdd.type = 'discount', sdd.total, 0))                                   as discount_sales,

        (coalesce(min(sp.promotion_value), 0) + coalesce(min(sdp.promotion_value), 0)) as promo
        ");

		$this->db->from('sales s');
		$this->db->join('outlets o', 's.outlet_fkid = o.outlet_id', 'left');
		$this->db->join('employee em', 'em.employee_id = s.employee_fkid', 'left');
		$this->db->join('open_shift os', 's.open_shift_fkid = os.open_shift_id', 'left');
		$this->db->join('shift sf', 'os.shift_fkid = sf.shift_id', 'left');
		$this->db->join('sales_detail sd', 'sd.sales_fkid = s.sales_id', 'left');
		$this->db->join('sales_detail_discount sdd', 'sd.sales_detail_id = sdd.sales_detail_fkid', 'left');
		$this->db->join('sales_void sv', 'sd.sales_detail_id = sv.sales_detail_fkid', 'left');
		$this->db->join("(select sales_fkid,
                    sum(if(category = 'tax', total, 0))      as tax,
                    sum(if(category = 'service', total, 0))  as service,
                    sum(if(category = 'voucher', total, 0))  as voucherTax,
                    sum(if(category = 'discount', total, 0)) as discountTax
            from sales_tax
            group by sales_fkid) stg", 'stg.sales_fkid = s.sales_id', 'left');
		$this->db->join('(SELECT SUM(promotion_value) as promotion_value, sales_fkid
            FROM sales_promotion
            group by sales_fkid) sp', 'sp.sales_fkid = s.sales_id', 'left');
		$this->db->join('(SELECT SUM(promotion_value) as promotion_value, sales_fkid
            FROM sales_detail_promotion
            where sales_detail_fkid IS NOT NULL
            group by sales_fkid) sdp', 'sdp.sales_fkid = s.sales_id', 'left');

		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('s.data_status', 'on');
		$this->db->where('sf.shift_id in ' . $parm['shift']);
		$this->db->where('o.outlet_id in ' . $parm['outlet']);

		switch ($parm['dataStatus']) {
			case 'success':
				$this->db->where('s.status', 'success');
				break;
			case 'refund':
				$this->db->where('s.status', 'refund');
				break;
		}
		if ($this->session->userdata('user_type') == "employee") {
			if (!empty($date_range) && $date_range > millis_to_localtime("Y-m-d", $parm['startDate'])) {
				if ($parm['dataType'] == '1') {
					$this->db->where("date_format(from_unixtime(((s.time_created / 1000 + " . $parm['timeZone'] . "))), '%Y-%m-%d') >=", $date_range);
					$this->db->where("date_format(from_unixtime(((s.time_created / 1000 + " . $parm['timeZone'] . "))), '%Y-%m-%d') <=", millis_to_localtime("Y-m-d", $parm['endDate']));
				} else {
					$this->db->where("date_format(from_unixtime(((os.time_open / 1000 + " . $parm['timeZone'] . "))), '%Y-%m-%d') >=", $date_range);
					$this->db->where("date_format(from_unixtime(((os.time_open / 1000 + " . $parm['timeZone'] . "))), '%Y-%m-%d') <=", millis_to_localtime("Y-m-d", $parm['endDate']));
				}
			} else {
				if ($parm['dataType'] == '1') {
					$this->db->where("s.time_created BETWEEN " . $parm['startDate'] . " AND " . $parm['endDate']);
				} else {
					$this->db->where("os.time_open BETWEEN " . $parm['startDate'] . " AND " . $parm['endDate']);
				}
			}
		} else {
			$this->db->where("s.time_created BETWEEN " . $parm['startDate'] . " AND " . $parm['endDate']);
		}

		$this->db->group_by('s.sales_id');
		$this->db->limit($parm['limit'], $parm['offset']);
		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", "query: " . $this->db->last_query() . "\n");
		return $result;
	}

	public function getPayment($sales_id)
	{
		$this->db->select('method,payment_id,pay');
		$this->db->from('sales_payment');
		$this->db->where('sales_fkid', $sales_id);
		return $this->db->get()->result_array();
	}

	public function getPayments($sales_ids)
	{
		if (count($sales_ids) == 0) {
			return array();
		}
		// $this->db->select('method,payment_id,pay');
		// $this->db->from('sales_payment');
		// $this->db->where_in('sales_fkid', $sales_ids);
		// return $this->db->get()->result_array();
		$this->db->select('coalesce(pmb.name, sp.method) as method, sp.pay, sp.sales_fkid');
		$this->db->from('sales_payment sp');
		$this->db->join('sales_payment_bank spb', 'spb.sales_payment_fkid = sp.payment_id', 'left');
		$this->db->join('payment_media_bank pmb', 'pmb.bank_id = spb.bank_fkid', 'left');
		$this->db->where_in('sp.sales_fkid', $sales_ids);
		$this->db->order_by('sp.sales_fkid', 'ASC');
		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", "query: " . $this->db->last_query() . "\n");
		return $result;
	}

	public function getPayDetail($payment_id)
	{
		$this->db->select('mb.name,sp.pay');
		$this->db->from('payment_media_bank mb');
		$this->db->join('sales_payment_bank sb', 'sb.bank_fkid = mb.bank_id', 'left');
		$this->db->join('sales_payment sp', 'sp.payment_id = sb.sales_payment_fkid', 'left');
		$this->db->where('sb.sales_payment_fkid', $payment_id);
		return $this->db->get()->row();
	}


	// json sukses v2
	public function jsonSuccsessV2($shift, $outlet, $timeZone, $startDate, $endDate, $dataType)
	{
		$this->db->select("
        s.sales_id AS nomor_nota,
        s.display_nota AS display_nota,
        s.time_created AS tanggal,
        s.status AS status,
        s.qty_customers AS pax,
        SUM(s.discount) AS discountSales,
        SUM(sd.discount) AS discountSd,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'discount' AND sales.sales_id = sd.sales_fkid),0) AS discountTax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'service' AND sales.sales_id = sd.sales_fkid),0) AS service,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'tax' AND sales.sales_id = sd.sales_fkid),0) AS tax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'voucher' AND sales.sales_id = sd.sales_fkid),0) AS voucherTax,

        s.voucher AS voucherSales,
        s.voucher_info AS keterangan_voucherS,
        s.discount_info AS keterangan_discountS,
        MIN(sd.discount_info) AS keterangan_discountSd,
        sum(sd.sub_total) AS sub_total,
        MIN(o.name) AS outlet_name,
        MIN(sf.name) AS shift_name,
        MIN(em.name) AS employee,
        (SELECT sum(sv.sub_total) from sales_void sv where sv.sales_fkid = s.sales_id) as sub_void,
        s.grand_total,
        sum((select sum(total) from sales_detail_discount where sales_detail_fkid = sd.sales_detail_id and type = 'discount')) as discount_sales
        ");

		$this->db->from('sales s');
		$this->db->join('outlets o', 's.outlet_fkid = o.outlet_id', 'left');
		$this->db->join('employee em', 'em.employee_id = s.employee_fkid', 'left');
		$this->db->join('open_shift os', 's.open_shift_fkid = os.open_shift_id', 'left');
		$this->db->join('shift sf', 'os.shift_fkid = sf.shift_id', 'left');
		// $this->db->join('sales_payment sp','sp.sales_fkid = s.sales_id','left');
		// $this->db->join('sales_tax st','st.sales_fkid = s.sales_id','left');
		// $this->db->join('gratuity g','g.gratuity_id = st.tax_fkid','left');
		$this->db->join('sales_detail sd', 'sd.sales_fkid = s.sales_id', 'left');

		$this->db->where('s.status', 'success');
		$this->db->where('s.data_status', 'on');
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));

		if (!empty($shift) || $shift != 0) {
			$this->db->where('shift_id', $shift);;
		}
		if (!empty($outlet) || $outlet != 0) {
			$this->db->where('outlet_id', $outlet);
		}
		if ($dataType == 1) {
			if (!empty($startDate)) {
				$this->db->where("date_format(from_unixtime((s.time_created / 1000 + " . $timeZone . ")), '%Y-%m-%d') >=", $startDate);
			}
			if (!empty($endDate)) {
				$this->db->where("date_format(from_unixtime((s.time_created / 1000 + " . $timeZone . ")), '%Y-%m-%d') <=", $endDate);
			}
		} else {
			if (!empty($startDate)) {
				$this->db->where("date_format(from_unixtime((os.time_open / 1000 + " . $timeZone . ")), '%Y-%m-%d') >=", $startDate);
			}
			if (!empty($endDate)) {
				$this->db->where("date_format(from_unixtime((os.time_open / 1000 + " . $timeZone . ")), '%Y-%m-%d') <=", $endDate);
			}
		}

		$this->db->group_by('s.sales_id');

		return $this->db->get()->result_array();
	}


	//json history revund V2
	public function jsonRefundV2($shift, $outlet, $timeZone, $startDate, $endDate, $dataType)
	{
		$this->db->select("
        s.sales_id AS nomor_nota,
        s.display_nota AS display_nota,
        sr.time_created AS tanggal,
        s.status AS status,
        s.qty_customers AS pax,
        SUM(s.discount) AS discountSales,
        SUM(sd.discount) AS discountSd,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'discount' AND sales.sales_id = sd.sales_fkid),0) AS discountTax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'service' AND sales.sales_id = sd.sales_fkid),0) AS service,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'tax' AND sales.sales_id = sd.sales_fkid),0) AS tax,

        IFNULL((SELECT SUM(sales_tax.total)
        FROM sales_tax JOIN gratuity ON gratuity.gratuity_id = sales_tax.tax_fkid JOIN sales ON sales.sales_id = sales_tax.sales_fkid
        WHERE gratuity.tax_category = 'voucher' AND sales.sales_id = sd.sales_fkid),0) AS voucherTax,

        (SELECT sum(sv.sub_total) from sales_void sv where sv.sales_fkid = s.sales_id) as sub_void,
        s.voucher AS voucherSales,
        s.voucher_info AS keterangan_voucherS,
        s.discount_info AS keterangan_discountS,
        MIN(sd.discount_info) AS keterangan_discountSd,
        sum(sd.sub_total) AS sub_total,
        MIN(o.name) AS outlet_name,
        MIN(sf.name) AS shift_name,
        MIN(em.name) AS employee,
        s.grand_total,
        sr.reason as ket_refund,
        sum((select sum(total) from sales_detail_discount where sales_detail_fkid = sd.sales_detail_id and type = 'discount')) as discount_sales
        ");

		$this->db->from('sales s');
		$this->db->join('outlets o', 's.outlet_fkid = o.outlet_id', 'left');
		$this->db->join('employee em', 'em.employee_id = s.employee_fkid', 'left');
		$this->db->join('open_shift os', 's.open_shift_fkid = os.open_shift_id', 'left');
		$this->db->join('shift sf', 'os.shift_fkid = sf.shift_id', 'left');
		// $this->db->join('sales_payment sp','sp.sales_fkid = s.sales_id','left');
		$this->db->join('sales_tax st', 'st.sales_fkid = s.sales_id', 'left');
		$this->db->join('gratuity g', 'g.gratuity_id = st.tax_fkid', 'left');
		$this->db->join('sales_detail sd', 's.sales_id = sd.sales_fkid', 'left');
		$this->db->join('sales_refund sr', 's.sales_id = sr.sales_fkid', 'left');


		$this->db->where('s.status', 'refund');
		$this->db->where('s.data_status', 'on');
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));

		if (!empty($shift) || $shift != 0) {
			$this->db->where('shift_id', $shift);;
		}
		if (!empty($outlet) || $outlet != 0) {
			$this->db->where('outlet_id', $outlet);
		}
		if ($dataType == 1) {
			if (!empty($startDate)) {
				$this->db->where("date_format(from_unixtime((sr.time_created / 1000 + " . $timeZone . ")), '%Y-%m-%d') >=", $startDate);
			}
			if (!empty($endDate)) {
				$this->db->where("date_format(from_unixtime((sr.time_created / 1000 + " . $timeZone . ")), '%Y-%m-%d') <=", $endDate);
			}
		} else {
			if (!empty($startDate)) {
				$this->db->where("date_format(from_unixtime((os.time_open / 1000 + " . $timeZone . ")), '%Y-%m-%d') >=", $startDate);
			}
			if (!empty($endDate)) {
				$this->db->where("date_format(from_unixtime((os.time_open / 1000 + " . $timeZone . ")), '%Y-%m-%d') <=", $endDate);
			}
		}

		$this->db->group_by('s.sales_id');

		return $this->db->get()->result_array();
	}

	// public function jsonDetailV2($param)
	// {
	// 	$this->db->select('s.display_nota as nomor_nota,o.name as outlet_name,
	//     sf.name as shift_name,e.name as employee,s.customer_name,COALESCE(e2.name,e.name) as oprator,
	//     p.sku,p.name as product_name,pc.name as category,ps.name as sub_category,sd.qty,
	//     (select sum(qty) from sales_void where sales_detail_fkid = sd.sales_detail_id) as qty_void,
	//     sd.price,sd.discount,
	//     (select SUM(total) from sales_detail_discount where `type` = \'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_sales,
	//     (select SUM(total) from sales_detail_discount where `type` = \'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_sales,
	//     (select SUM(total) from sales_detail_discount where `type` = \'promotion\' and sales_detail_fkid=sd.sales_detail_id) as promo,
	//     (SELECT SUM(total) from sales_detail_tax where category=\'tax\' and sales_detail_fkid=sd.sales_detail_id) as tax,
	//     (SELECT SUM(total) from sales_detail_tax where category=\'service\' and sales_detail_fkid=sd.sales_detail_id) as service,
	//     (SELECT SUM(total) from sales_detail_tax where category=\'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_tax,
	//     (SELECT SUM(total) from sales_detail_tax where category=\'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_tax,
	//     s.grand_total,s.status,sd.sales_detail_id,s.sales_id, 
	//     concat_ws(", ", if(LENGTH(s.discount_info), s.discount_info, NULL), s.voucher_info) as keterangan_discount,
	//     FROM_UNIXTIME(sd.time_created/1000+' . $param['timeZone'] . ', "%d-%m-%Y %H:%i") as tanggal,')
	// 		->from('sales_detail sd')
	// 		->join('sales s', 's.sales_id = sd.sales_fkid')
	// 		->join('outlets o', 'o.outlet_id = s.outlet_fkid')
	// 		->join('open_shift os', 'os.open_shift_id = s.open_shift_fkid')
	// 		->join('shift sf', 'sf.shift_id = os.shift_fkid')
	// 		->join('employee e', 'e.employee_id = s.employee_fkid', 'LEFT')
	// 		->join('employee e2', 'e2.employee_id = sd.employee_fkid', 'LEFT')
	// 		->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid')
	// 		->join('products p', 'p.product_id = pd.product_fkid')
	// 		->join('products_category pc', 'pc.product_category_id = p.product_category_fkid')
	// 		->join('products_subcategory ps', 'ps.product_subcategory_id = p.product_subcategory_fkid')
	// 		->where('o.admin_fkid', $this->session->userdata('admin_id'))
	// 		->where('s.outlet_fkid in ' . $param['outlet'])
	// 		->where('sf.shift_id in ' . $param['shift'])
	// 		// ->where('s.time_created >=', $param['startDate'])
	// 		// ->where('s.time_created <=', $param['endDate'])
	// 		->where('sd.sales_detail_id >', (int)$param['offsetId'])
	// 		// ->group_by('sd.sales_detail_id')
	// 		->order_by('sd.sales_detail_id', 'ASC')
	// 		->limit(100);



	// 	if ($param['id']){
	// 		$this->db->where("s.display_nota ", $param['id']);
	// 	}else{
	// 		if ($param['dataType'] == 1) {
	// 		$this->db->where("s.time_created BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
	// 	} else {
	// 		$this->db->where("os.time_open BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
	// 	}
	// 	}

	// 	$result = $this->db->get()->result_array();
	// 	file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . $this->db->last_query() . "\n");
	// 	return $result;
	// }
	public function jsonDetailV2($param)
	{
		$this->db->select('s.display_nota as nomor_nota,s.member_fkid,o.name as outlet_name,
        sf.name as shift_name,e.name as employee,s.customer_name,COALESCE(e2.name,e.name) as oprator,
        p.sku,CONCAT(p.name, COALESCE(CONCAT(" (",pdv.variant_name,")"),"")) as product_name,pc.name as category,ps.name as sub_category,sd.qty,
        (select sum(qty) from sales_void where sales_detail_fkid = sd.sales_detail_id) as qty_void,
        sd.price,sd.discount,
        (select SUM(total) from sales_detail_discount where `type` = \'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_sales,
        (select SUM(total) from sales_detail_discount where `type` = \'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_sales,
        (select SUM(total) from sales_detail_discount where `type` = \'promotion\' and sales_detail_fkid=sd.sales_detail_id) as promo,
		(SELECT SUM(promotion_value) from sales_detail_promotion where sales_detail_fkid = sd.sales_detail_id) as promo_item,
        (SELECT SUM(total) from sales_detail_tax where category=\'tax\' and sales_detail_fkid=sd.sales_detail_id) as tax,
        (SELECT SUM(total) from sales_detail_tax where category=\'service\' and sales_detail_fkid=sd.sales_detail_id) as service,
        (SELECT SUM(total) from sales_detail_tax where category=\'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_tax,
        (SELECT SUM(total) from sales_detail_tax where category=\'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_tax,
        s.grand_total,s.status,sd.sales_detail_id,s.sales_id, 
        concat_ws(", ", if(LENGTH(s.discount_info), s.discount_info, NULL), s.voucher_info) as keterangan_discount,
        FROM_UNIXTIME(sd.time_created/1000+' . $param['timeZone'] . ', "%d-%m-%Y %H:%i:%s") as tanggal,')
			->from('sales_detail sd')
			->join('sales s', 's.sales_id = sd.sales_fkid')
			->join('outlets o', 'o.outlet_id = s.outlet_fkid')
			->join('open_shift os', 'os.open_shift_id = s.open_shift_fkid')
			->join('shift sf', 'sf.shift_id = os.shift_fkid')
			->join('employee e', 'e.employee_id = s.employee_fkid', 'LEFT')
			->join('employee e2', 'e2.employee_id = sd.employee_fkid', 'LEFT')
			->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid')
			->join('products p', 'p.product_id = pd.product_fkid')
			->join('products_category pc', 'pc.product_category_id = p.product_category_fkid')
			->join('products_subcategory ps', 'ps.product_subcategory_id = p.product_subcategory_fkid')
			->join('products_detail_variant pdv', 'pdv.variant_id=pd.variant_fkid', 'LEFT')
			->where('o.admin_fkid', $this->session->userdata('admin_id'))
			->where('s.outlet_fkid in ' . $param['outlet'])
			->where('sf.shift_id in ' . $param['shift'])
			// ->where('s.time_created >=', $param['startDate'])
			// ->where('s.time_created <=', $param['endDate'])
			->where('sd.sales_detail_id >', (int)$param['offsetId'])
			// ->group_by('sd.sales_detail_id')
			->order_by('sd.sales_detail_id', 'ASC')
			->limit(100);

		if ($param['startDate'] != '' && $param['endDate'] != '') {
			if ($param['dataType'] == '1') {
				$this->db->where("s.time_created BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
			} else {
				$this->db->where("os.time_open BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
			}
		}
		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . preg_replace('/\s+/', ' ', $this->db->last_query()) . "\n");
		return $result;
	}

	public function jsonFromDetail($param)
	{
		$dates = $this->extractDateFromString($param['nomor_nota']);
		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> dates: " . json_encode($dates) . "\n");
		$millis = $this->getTimeMillisecondsWithDuration($dates['date'], 1);
		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> millis: " . json_encode($millis) . "\n");

		$this->db->select('s.display_nota as nomor_nota,o.name as outlet_name,
        sf.name as shift_name,e.name as employee,s.customer_name,COALESCE(e2.name,e.name) as oprator,
        p.sku,p.name as product_name,pc.name as category,ps.name as sub_category,sd.qty,
        (select sum(qty) from sales_void where sales_detail_fkid = sd.sales_detail_id) as qty_void,
        sd.price,sd.discount,
        (select SUM(total) from sales_detail_discount where `type` = \'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_sales,
        (select SUM(total) from sales_detail_discount where `type` = \'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_sales,
        (select SUM(total) from sales_detail_discount where `type` = \'promotion\' and sales_detail_fkid=sd.sales_detail_id) as promo,
        (SELECT SUM(total) from sales_detail_tax where category=\'tax\' and sales_detail_fkid=sd.sales_detail_id) as tax,
        (SELECT SUM(total) from sales_detail_tax where category=\'service\' and sales_detail_fkid=sd.sales_detail_id) as service,
        (SELECT SUM(total) from sales_detail_tax where category=\'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_tax,
        (SELECT SUM(total) from sales_detail_tax where category=\'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_tax,
        s.grand_total,s.status,sd.sales_detail_id,s.sales_id, 
        concat_ws(", ", if(LENGTH(s.discount_info), s.discount_info, NULL), s.voucher_info) as keterangan_discount,
        FROM_UNIXTIME(sd.time_created/1000+25200, "%d-%m-%Y %H:%i:%s") as tanggal,')
			->from('sales_detail sd')
			->join('sales s', 's.sales_id = sd.sales_fkid')
			->join('outlets o', 'o.outlet_id = s.outlet_fkid')
			->join('open_shift os', 'os.open_shift_id = s.open_shift_fkid')
			->join('shift sf', 'sf.shift_id = os.shift_fkid')
			->join('employee e', 'e.employee_id = s.employee_fkid', 'LEFT')
			->join('employee e2', 'e2.employee_id = sd.employee_fkid', 'LEFT')
			->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid')
			->join('products p', 'p.product_id = pd.product_fkid')
			->join('products_category pc', 'pc.product_category_id = p.product_category_fkid')
			->join('products_subcategory ps', 'ps.product_subcategory_id = p.product_subcategory_fkid')
			->where('s.display_nota', $param['nomor_nota'], $this->session->userdata('admin_id'))

			// ->where('s.nomor_nota',$this->session->userdata('admin_id'))
			// ->where('s.outlet_fkid in ' . $param['outlet'])
			// ->where('sf.shift_id in ' . $param['shift'])
			// ->where('s.time_created >=', '1670000400000')
			// ->where('s.time_created <=', '1672505999999')

			->where('sd.sales_detail_id >', (int)$param['offsetId'])


			// ->group_by('sd.sales_detail_id')
			->order_by('sd.sales_detail_id', 'ASC')
			->limit(100);


		if ($param['startDate'] != '' && $param['endDate'] != '') {
			if ($param['dataType'] == '1') {
				$this->db->where("s.time_created BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
			} else {
				$this->db->where("os.time_open BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
			}
		} else if (isset($millis['start'])) {
			$this->db->where("s.time_created BETWEEN " . $millis['start'] . " AND " . $millis['end']);
		}

		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . preg_replace('/[\n\r\t]/', ' ', $this->db->last_query()) . "\n");
		return $result;
	}

	function extractDateFromString($string)
	{
		// Define the regular expression pattern to match the date format
		$pattern = '/^[A-Za-z]{2,4}(\d{4})(\d{2})(\d{2})\d+$/';

		// Use preg_match to match the pattern against the input string
		if (preg_match($pattern, $string, $matches)) {
			// Extract the year, month, and day from the matches array
			$year = $matches[1];
			$month = $matches[2];
			$day = $matches[3];

			// Return the extracted date as an associative array
			return [
				'year' => $year,
				'month' => $month,
				'day' => $day,
				'date' => $year . '-' . $month . '-' . $day,
			];
		} else {
			// Return an error message if the input string does not match the pattern
			return ['error' => 'Invalid input string format'];
		}
	}

	function getTimeMillisecondsWithDuration($date, $duration)
	{
		// Parse the input date string into a DateTime object
		$dateObject = DateTime::createFromFormat('Y-m-d', $date);

		// Calculate the start and end dates based on the duration
		// $durationObject = DateInterval::createFromDateString($duration);
		$durationObject = new DateInterval('P' . intval($duration) . 'D');
		$startDateObject = clone $dateObject;
		$startDateObject->sub($durationObject);
		$endDateObject = clone $dateObject;
		$endDateObject->add($durationObject);

		// Convert the start and end dates to timestamps in milliseconds
		$startTimestamp = $startDateObject->getTimestamp() * 1000;
		$endTimestamp = $endDateObject->getTimestamp() * 1000;

		// Return an associative array with the start and end timestamps
		return [
			'start' => $startTimestamp,
			'end' => $endTimestamp
		];
	}

	public function jsonFromMember($param)
	{
		$this->db->select('s.display_nota as nomor_nota,s.member_fkid,o.name as outlet_name,
        sf.name as shift_name,e.name as employee,s.customer_name,COALESCE(e2.name,e.name) as oprator,
        p.sku,p.name as product_name,pc.name as category,ps.name as sub_category,sd.qty,
        (select sum(qty) from sales_void where sales_detail_fkid = sd.sales_detail_id) as qty_void,
        sd.price,sd.discount,
        (select SUM(total) from sales_detail_discount where `type` = \'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_sales,
        (select SUM(total) from sales_detail_discount where `type` = \'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_sales,
        (select SUM(total) from sales_detail_discount where `type` = \'promotion\' and sales_detail_fkid=sd.sales_detail_id) as promo,
        (SELECT SUM(total) from sales_detail_tax where category=\'tax\' and sales_detail_fkid=sd.sales_detail_id) as tax,
        (SELECT SUM(total) from sales_detail_tax where category=\'service\' and sales_detail_fkid=sd.sales_detail_id) as service,
        (SELECT SUM(total) from sales_detail_tax where category=\'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_tax,
        (SELECT SUM(total) from sales_detail_tax where category=\'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_tax,
        s.grand_total,s.status,sd.sales_detail_id,s.sales_id, 
        concat_ws(", ", if(LENGTH(s.discount_info), s.discount_info, NULL), s.voucher_info) as keterangan_discount,
        FROM_UNIXTIME(sd.time_created/1000+25200, "%d-%m-%Y %H:%i:%s") as tanggal,')
			->from('sales_detail sd')
			->join('sales s', 's.sales_id = sd.sales_fkid')
			->join('outlets o', 'o.outlet_id = s.outlet_fkid')
			->join('open_shift os', 'os.open_shift_id = s.open_shift_fkid')
			->join('shift sf', 'sf.shift_id = os.shift_fkid')
			->join('employee e', 'e.employee_id = s.employee_fkid', 'LEFT')
			->join('employee e2', 'e2.employee_id = sd.employee_fkid', 'LEFT')
			->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid')
			->join('products p', 'p.product_id = pd.product_fkid')
			->join('products_category pc', 'pc.product_category_id = p.product_category_fkid')
			->join('products_subcategory ps', 'ps.product_subcategory_id = p.product_subcategory_fkid')
			->where('s.member_fkid', $param['member_id'], $this->session->userdata('admin_id'))

			// ->where('s.nomor_nota',$this->session->userdata('admin_id'))
			// ->where('s.outlet_fkid in ' . $param['outlet'])
			// ->where('sf.shift_id in ' . $param['shift'])
			// ->where('s.time_created >=', '1670000400000')
			// ->where('s.time_created <=', '1672505999999')

			->where('sd.sales_detail_id >', (int)$param['offsetId'])


			// ->group_by('sd.sales_detail_id')
			->order_by('sd.sales_detail_id', 'ASC')
			->limit(100);

		if ($param['startDate'] != '' && $param['endDate'] != '') {
			if ($param['dataType'] == '1') {
				$this->db->where("s.time_created BETWEEN " . $param['1672678800000'] . " AND " . $param['1673369999999']);
			} else {
				$this->db->where("os.time_open BETWEEN " . $param['1672678800000'] . " AND " . $param['1673369999999']);
			}
		}

		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . preg_replace('/[\n\r\t]/', ' ', $this->db->last_query()) . "\n");
		return $result;
	}
	public function jsonFromGroup($param)
	{
		$admin  = $this->session->userdata('admin_id');
		$gender = $this->input->post('gender');
		$province = $this->input->post('province');
		$membertype = $this->input->post('membertype');
		$outlets = $this->input->post('outlets');
		$provincerepar = "";
		$membertyperepar = "";
		$outletsrepar = "";
		$repairgender = "";

		if ($gender == "Male") {
			$repairgender = "gender = 1";
		} else if ($gender == "Female") {
			$repairgender = "gender = 2";
		} else {
			$repairgender = "gender IS NOT NULL";
		}
		if ($this->input->post('membertype')) {
			$membertyperepar = "memberType = '$membertype'";
		} else {
			$membertyperepar = " memberType IS NOT NULL";
		}
		if ($this->input->post('outlets')) {
			$outletsrepar = "outlets = '$outlets'";
		} else {
			$outletsrepar = " outlets IS NOT NULL";
		}
		if ($this->input->post('province')) {
			$provincerepar = "province = '$province'";
		} else {
			$provincerepar = " province IS NOT NULL";
		}


		$this->db->select('s.display_nota as nomor_nota,ANY_VALUE(m.gender) AS gender,
		ANY_VALUE(m.province) AS province,
		ANY_VALUE(v.name) AS memberType,
		ANY_VALUE(o.name) AS outlets,s.member_fkid,o.name as outlet_name,
        sf.name as shift_name,e.name as employee,s.customer_name,COALESCE(e2.name,e.name) as oprator,
        p.sku,p.name as product_name,pc.name as category,ps.name as sub_category,sd.qty,
        (select sum(qty) from sales_void where sales_detail_fkid = sd.sales_detail_id) as qty_void,
        sd.price,sd.discount,
        (select SUM(total) from sales_detail_discount where `type` = \'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_sales,
        (select SUM(total) from sales_detail_discount where `type` = \'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_sales,
        (select SUM(total) from sales_detail_discount where `type` = \'promotion\' and sales_detail_fkid=sd.sales_detail_id) as promo,
        (SELECT SUM(total) from sales_detail_tax where category=\'tax\' and sales_detail_fkid=sd.sales_detail_id) as tax,
        (SELECT SUM(total) from sales_detail_tax where category=\'service\' and sales_detail_fkid=sd.sales_detail_id) as service,
        (SELECT SUM(total) from sales_detail_tax where category=\'voucher\' and sales_detail_fkid=sd.sales_detail_id) as voucher_tax,
        (SELECT SUM(total) from sales_detail_tax where category=\'discount\' and sales_detail_fkid=sd.sales_detail_id) as disc_tax,
        s.grand_total,s.status,sd.sales_detail_id,s.sales_id, 
        concat_ws(", ", if(LENGTH(s.discount_info), s.discount_info, NULL), s.voucher_info) as keterangan_discount,
        FROM_UNIXTIME(sd.time_created/1000+25200, "%d-%m-%Y %H:%i:%s") as tanggal,')
			->from('sales_detail sd')
			->join('sales s', 's.sales_id = sd.sales_fkid')
			->join('members m', ' s.member_fkid = m.member_id')
			->join('members_detail md', 'md.member_fkid = m.member_id')
			->join('members_type mt', ' mt.type_id = md.type_fkid')
			->join('products v', ' v.product_id = mt.product_fkid')
			->join('outlets o', 'o.outlet_id = s.outlet_fkid')
			->join('open_shift os', 'os.open_shift_id = s.open_shift_fkid')
			->join('shift sf', 'sf.shift_id = os.shift_fkid')
			->join('employee e', 'e.employee_id = s.employee_fkid', 'LEFT')
			->join('employee e2', 'e2.employee_id = sd.employee_fkid', 'LEFT')
			->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid')
			->join('products p', 'p.product_id = pd.product_fkid')
			->join('products_category pc', 'pc.product_category_id = p.product_category_fkid')
			->join('products_subcategory ps', 'ps.product_subcategory_id = p.product_subcategory_fkid')
			->where('md.admin_fkid ', $admin)
			->where($repairgender)
			->having($provincerepar)
			->having($membertyperepar)
			->having($outletsrepar)
			->limit($param['limit'], $param['offset']);
		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . $province . "\n");

		if ($param['startDate'] != '' && $param['endDate'] != '') {
			if ($param['dataType'] == '1') {
				$this->db->where("s.time_created BETWEEN " . $param['1672678800000'] . " AND " . $param['1673369999999']);
			} else {
				$this->db->where("os.time_open BETWEEN " . $param['1672678800000'] . " AND " . $param['1673369999999']);
			}
		}


		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . preg_replace('/[\n\r\t]/', ' ', $this->db->last_query()) . "\n");
		return $result;
	}


	//json detail 
	public function jsonDetail($param)
	{
		$this->db->select("
			s.display_nota as nomor_nota,
            o.name AS outlet_name,
            sf.name AS shift_name,
            s.customer_name AS customer_name,
            s.sales_id AS nomor_nota,
            s.display_nota AS display_nota,
            s.time_created AS tanggal,
            s.status AS status,
            s.qty_customers AS pax,
            s.discount AS discountSales,
            s.grand_total AS grand_total,
            s.voucher AS voucherSales,
            s.voucher_info AS keterangan_voucherS,
            s.discount_info AS keterangan_discountS,
            s.sales_id,
            sd.price AS price,
            sd.qty AS qty,
            sd.discount_info AS keterangan_discountSd,
            sd.sub_total AS sub_total,
            sd.discount AS discountSd,
            p.name AS product_name,
            p.sku AS sku,
            sd.product_detail_fkid,
            sd.sales_detail_id,
            sc.name as sub_category,
            c.name as category,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='service' and tx.sales_detail_fkid=sd.sales_detail_id),0) as service,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='tax' and tx.sales_detail_fkid=sd.sales_detail_id),0) as tax,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='discount' and tx.sales_detail_fkid=sd.sales_detail_id),0) as discountTax,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='voucher' and tx.sales_detail_fkid=sd.sales_detail_id),0) as voucherTax,

            emd.name AS oprator,
            em.name AS employee,
            ifnull(pdv.variant_name,'') as variant,
            ifnull(sum((select sdc.total as dis_detail where sdc.type='discount' and sdc.sales_detail_fkid=sd.sales_detail_id)),0) as dis_detail,

            ifnull(sum((select sdc.total as dis_detail where sdc.type='voucher' and sdc.sales_detail_fkid=sd.sales_detail_id)),0) as vo_detail,

            (SELECT (ifnull((SELECT promotion_value FROM sales_detail_promotion sdp WHERE sdp.sales_detail_fkid IS NOT NULL AND sdp.sales_detail_fkid = sd.sales_detail_id ),0)+IFNULL(sum((SELECT sum(total) FROM sales_detail_discount sdd WHERE sdd.sales_detail_fkid = sd.sales_detail_id AND sdd.type = 'promotion')),0))) AS promo

        ");
		$this->db->from('sales_detail sd');
		$this->db->join('sales s', 's.sales_id = sd.sales_fkid', 'left');
		$this->db->join('products p', 'sd.product_fkid = p.product_id', 'left');
		$this->db->join('products_subcategory sc', 'sc.product_subcategory_id = p.product_subcategory_fkid', 'left');
		$this->db->join('products_category c', 'c.product_category_id = p.product_category_fkid', 'left');
		$this->db->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid', 'left');
		$this->db->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');
		$this->db->join('sales_detail_discount sdc', 'sdc.sales_detail_fkid = sd.sales_detail_id', 'left');
		$this->db->join('employee em', 'em.employee_id = s.employee_fkid', 'left');
		$this->db->join('employee emd', 'emd.employee_id = sd.employee_fkid', 'left');
		$this->db->join('open_shift os', 's.open_shift_fkid = os.open_shift_id', 'left');
		$this->db->join('shift sf', 'os.shift_fkid = sf.shift_id', 'left');
		$this->db->join('outlets o', 's.outlet_fkid = o.outlet_id', 'left');
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('s.data_status', 'on');
		$this->db->where('sf.shift_id in ' . $param['shift']);
		$this->db->where('o.outlet_id in ' . $param['outlet']);
		if ($param['startDate'] != '' && $param['endDate'] != '') {
			if ($param['dataType'] == '1') {
				$this->db->where("s.time_created BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
			} else {
				$this->db->where("os.time_open BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
			}
		}
		$this->db->group_by('sd.sales_detail_id');
		$this->db->limit($param['limit'], $param['offset']);
		$result =  $this->db->get()->result_array();

		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . preg_replace('/[\n\r\t]/', ' ', $this->db->last_query()) . "\n");
		return $result;
	}


	public function serverDetail($param)
	{
		$this->db->select("
            o.admin_fkid AS admin_fkid,
			s.display_nota as nomor_nota,
            o.name AS outlet_name,
            sf.name AS shift_name,
            s.customer_name AS customer_name,
            s.sales_id AS nomor_nota,
            s.display_nota AS display_nota,
            s.time_created AS tanggal,
            s.status AS status,
            s.qty_customers AS pax,
            s.discount AS discountSales,
            s.grand_total AS grand_total,
            s.voucher AS voucherSales,
            s.voucher_info AS keterangan_voucherS,
            s.discount_info AS keterangan_discountS,
            MIN(sp.method) AS pembayaran,
            sd.price AS price,
            sd.qty AS qty,
            sd.discount_info AS keterangan_discountSd,
            sd.sub_total AS sub_total,
            sd.discount AS discountSd,
            p.name AS product_name,
            p.sku AS sku,
            IFNULL(SUM((SELECT sdt.total FROM DUAL WHERE g.tax_category = 'tax')),0) AS tax,
            IFNULL(SUM((SELECT sdt.total FROM DUAL WHERE g.tax_category = 'discount')),0) AS discountTax,
            IFNULL(SUM((SELECT sdt.total FROM DUAL WHERE g.tax_category = 'service')),0) AS service,
            IFNULL(SUM((SELECT sdt.total FROM DUAL WHERE g.tax_category = 'voucher')),0) AS voucherTax,
            emd.name AS oprator,
            (SELECT sum(sv.qty) from sales_void sv WHERE sv.product_detail_fkid=sd.product_detail_fkid and sv.sales_detail_fkid = sd.sales_detail_id) as qty_void,
            (SELECT sum(sv.sub_total) from sales_void sv WHERE sv.product_detail_fkid=sd.product_detail_fkid and sv.sales_detail_fkid = sd.sales_detail_id) as sub_void,
            em.name AS employee,
            ifnull(pdv.variant_name,'') as variant,
            ");
		$this->db->from('sales_detail sd');
		$this->db->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid', 'left');
		$this->db->join('sales s', 's.sales_id = sd.sales_fkid', 'left');
		$this->db->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');
		$this->db->join('products p', 'sd.product_fkid = p.product_id', 'left');
		$this->db->join('sales_void sv', 'sv.product_fkid = p.product_id', 'left');
		$this->db->join('sales_payment sp', 'sp.sales_fkid = s.sales_id', 'left');
		$this->db->join('employee em', 'em.employee_id = s.employee_fkid', 'left');
		$this->db->join('employee emd', 'emd.employee_id = sd.employee_fkid', 'left');
		$this->db->join('open_shift os', 's.open_shift_fkid = os.open_shift_id', 'left');
		$this->db->join('shift sf', 'os.shift_fkid = sf.shift_id', 'left');
		$this->db->join('sales_detail_tax sdt', 'sdt.sales_detail_fkid = sd.sales_detail_id', 'left');
		$this->db->join('gratuity g', 'g.gratuity_id = sdt.tax_fkid', 'left');
		$this->db->join('outlets o', 's.outlet_fkid = o.outlet_id', 'left');
		$this->db->where('s.data_status', 'on');
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('sf.shift_id in ' . $param['shift']);
		$this->db->where('o.outlet_id in ' . $param['outlet']);

		if ($param['startDate'] != '' && $param['endDate'] != '') {
			if ($param['dataType'] == '1') {
				$this->db->where("s.time_created BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
			} else {
				$this->db->where("os.time_open BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
			}
		}

		$this->db->group_by('sd.sales_detail_id');
		$this->db->limit($param['limit'], $param['offset']);
		return $this->db->get()->result_array();
	}


	//report void
	public function jsonVoidDetail($param)
	{
		$this->db->select("
                o.admin_fkid as admin_fkid,
				s.display_nota as nomor_nota,
                o.outlet_id AS outlet_id,
                s.customer_name as customer_name,
                s.sales_id AS nomor_nota,
                s.display_nota AS display_nota,
                s.time_created AS tanggal,
                s.status as status,
                p.name as product_name,
                p.sku as sku,
                s.qty_customers AS pax,
                o.name AS outlet_name,
                sf.name AS shift_name,
                sv.qty as qty_void,
                (sv.price*-1) as price_void,
                (sv.sub_total*-1) as sub_void,
                sv.info as info_void,
                sv.sales_void_id as id,
                em.name as employee,
                ifnull(pdv.variant_name,'') as variant,
            ");
		$this->db->from('sales s');
		$this->db->join('outlets o', 's.outlet_fkid = o.outlet_id', 'left');
		$this->db->join('employee em', 'em.employee_id = s.employee_fkid', 'left');
		$this->db->join('open_shift os', 's.open_shift_fkid=os.open_shift_id', 'left');
		$this->db->join('shift sf', 'os.shift_fkid=sf.shift_id', 'left');
		$this->db->join('sales_void sv', 'sv.sales_fkid=s.sales_id', 'left');
		$this->db->join('products p', 'sv.product_fkid=p.product_id', 'left');
		$this->db->join('products_detail pd', 'pd.product_detail_id = sv.product_detail_fkid', 'left');
		$this->db->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where('sv.qty !=', 'null');
		$this->db->where('s.data_status', 'on');
		$this->db->where('shift_id in ' . $param['shift']);
		$this->db->where('outlet_id in ' . $param['outlet']);
		if ($param['dataType'] == '1') {
			$this->db->where("s.time_created BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
		} else {
			$this->db->where("os.time_open BETWEEN " . $param['startDate'] . " AND " . $param['endDate']);
		}
		$this->db->limit($param['limit'], $param['offset']);
		return $this->db->get()->result_array();
	}

	public function getVoid($sales_id, $product_id)
	{
		$this->db->select("
            sum(sv.qty) as qty_void,
            sum(sv.sub_total) as sub_void
        ");
		$this->db->from('sales_void sv');
		$this->db->join('sales s', 's.sales_id = sv.sales_fkid', 'left');
		$this->db->where('sv.sales_fkid', $sales_id);
		$this->db->where('sv.product_detail_fkid', $product_id);
		// $this->db->where('sv.sales_detail_fkid', $sales_detail_id);
		$this->db->where('s.status', 'success');
		return $this->db->get()->row();
	}

	public function getvoid_BySalesDetail($sales_detail_id, $product_detail_fkid, $sales_id)
	{
		$this->db->select("
            sum(sv.qty) as qty_void,
            sum(sv.sub_total) as sub_void
        ");
		$this->db->where('sv.sales_detail_fkid', $sales_detail_id);
		$this->db->where('sv.product_detail_fkid', $product_detail_fkid);
		// $this->db->where('sv.sales_fkid', $sales_id);
		return $this->db->get('sales_void sv')->row();
	}

	public function get_outlet_by_id($outlet_id)
	{
		$this->db->select("*");
		$this->db->from("outlets");
		$this->db->where("outlet_id", $outlet_id);

		return $this->db->get();
	}

	public function get_data_print_nota($display_nota, $outlet_id, $start_date, $end_date)
	{
		$this->db->select(
			"
			sts.payment_status,
			s.display_nota AS nomor_nota,
			o.name AS outlet_name,
			sf.name AS shift_name,
			e.name AS employee,
			o.outlet_id,
			s.customer_name,
			m.address AS member_address,
			m.city AS member_city,
			COALESCE(e2.name, e.name) AS operator,
			p.sku,
			CONCAT (
				p.name,
				COALESCE(CONCAT (' (', pdv.variant_name, ')'), '')
			) as product_name,
			p.description AS product_description,
			p.photo AS product_image,
			pc.name AS category,
			ps.name AS sub_category,
			sd.qty,
			sd.price,
			sd.discount,
			(
				SELECT
					SUM(qty)
				FROM
					sales_void
				WHERE
					sales_detail_fkid = sd.sales_detail_id
			) AS qty_void,
			(
				SELECT
					SUM(total)
				FROM
					sales_detail_discount
				WHERE
					type='discount'
					AND sales_detail_fkid=sd.sales_detail_id
			) AS disc_sales,
			(
				SELECT
					SUM(total)
				FROM
					sales_detail_discount
				WHERE
					type='voucher'
					AND sales_detail_fkid=sd.sales_detail_id
			) AS voucher_sales,
			(
				select
					SUM(total)
				from
					sales_detail_discount
				where
					`type` = 'promotion'
					and sales_detail_fkid = sd.sales_detail_id
			) as promo,
			(
				SELECT
					SUM(total)
				from
					sales_detail_tax
				where
					category = 'tax'
					and sales_detail_fkid = sd.sales_detail_id
			) as tax,
			(
				SELECT
					SUM(total)
				from
					sales_detail_tax
				where
					category = 'service'
					and sales_detail_fkid = sd.sales_detail_id
			) as service,
			(
				SELECT
					SUM(total)
				from
					sales_detail_tax
				where
					category = 'voucher'
					and sales_detail_fkid = sd.sales_detail_id
			) as voucher_tax,
			(
				SELECT
					SUM(total)
				from
					sales_detail_tax
				where
					category = 'discount'
					and sales_detail_fkid = sd.sales_detail_id
			) as disc_tax,
			s.grand_total,
			s.status,
			sd.sales_detail_id,
			s.sales_id,
			concat_ws (
				', ',
				if ( 
					LENGTH (s.discount_info),
					`s`.`discount_info`,
					NULL
				),
				s.voucher_info,
				sd.discount_info
			) as keterangan_discount,
			FROM_UNIXTIME (
				sd.time_created / 1000 + 25200,
				'%d-%m-%Y %H:%i:%s'
			) as tanggal
			"
		);
		$this->db->from("sales_detail sd");
		$this->db->join("sales s", "s.sales_id=sd.sales_fkid");
		$this->db->join("
			(
				SELECT 
					IF(COALESCE(p.unpaid, 0) > 0, 'unpaid', 'paid') AS payment_status,
					sj.sales_id 
				FROM
					sales sj
					LEFT JOIN piutang p ON p.sales_fkid = sj.sales_id
				WHERE
					sj.display_nota = '{$display_nota}'
			) AS sts
		", "sts.sales_id = s.sales_id", "LEFT");
		$this->db->join("members m", "m.member_id=s.member_fkid", "LEFT");
		$this->db->join("outlets o", "o.outlet_id=s.outlet_fkid");
		$this->db->join("open_shift os", "os.open_shift_id=s.open_shift_fkid");
		$this->db->join("shift sf", "sf.shift_id=os.shift_fkid");
		$this->db->join("employee e", "e.employee_id=s.employee_fkid", "LEFT");
		$this->db->join("employee e2", "e2.employee_id=sd.employee_fkid", "LEFT");
		$this->db->join("products_detail pd", "pd.product_detail_id=sd.product_detail_fkid");
		$this->db->join("products p", "p.product_id=pd.product_fkid");
		$this->db->join("products_detail_variant pdv", "pdv.product_fkid = p.product_id", "LEFT");
		$this->db->join("products_category pc", "pc.product_category_id=p.product_category_fkid");
		$this->db->join("products_subcategory ps", "ps.product_subcategory_id=p.product_subcategory_fkid");
		$this->db->where("s.display_nota", $display_nota);
		$this->db->where(["sd.sales_detail_id >" => 0]);
		$this->db->like("o.name", $outlet_id);
		$this->db->where("s.time_created BETWEEN '{$start_date}' AND '{$end_date}'");

		$result =  $this->db->get();
		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . preg_replace('/[\n\r\t]/', ' ', $this->db->last_query()) . "\n");
		return $result;
	}
}

/* End of file Report_sales_history.php */
/* Location: ./application/models/report/Report_sales_history.php */