<?php
defined('BASEPATH') or exit('No direct script access allowed');

class M_report_cogs extends CI_Model
{

	public function getDataCogs($startDate, $endDate, $timeZone, $category, $outlet)
	{
		$this->db->select("
			o.name as outlet_name,
			concat(p.name,' ',ifnull(pdv.variant_name,'')) product_name,
			p.sku,
			pdv.variant_sku,
			ps.name as sub_category,
			c.name as category,
			sum(sd.qty) as qty,
			pd.price_buy as hpp,
			pd.price_sell as price_sell,
			pd.product_detail_id,
			sum(sd.price) as price_sd,
			sum(sd.discount) AS discountSd,
			sum(sd.sub_total) AS sub_total,

			ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='service' and tx.sales_detail_fkid=max(sd.sales_detail_id)),0) as service,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='tax' and tx.sales_detail_fkid=max(sd.sales_detail_id)),0) as tax,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='discount' and tx.sales_detail_fkid=max(sd.sales_detail_id)),0) as discountTax,

            ifnull((select sum(total) from sales_detail_tax tx left join gratuity gt on gt.gratuity_id=tx.tax_fkid where gt.tax_category='voucher' and tx.sales_detail_fkid=max(sd.sales_detail_id)),0) as voucherTax,

            ifnull(sum((select sdc.total as dis_detail where sdc.type='discount' and sdc.sales_detail_fkid=sd.sales_detail_id)),0) as dis_detail,

            ifnull(sum((select sdc.total as dis_detail where sdc.type='voucher' and sdc.sales_detail_fkid=sd.sales_detail_id)),0) as vo_detail,

			sum((select sum(qty) from sales_void sv where sv.product_detail_fkid=sd.product_detail_fkid and sv.sales_fkid=s.sales_id and sv.sales_detail_fkid=sd.sales_detail_id)) as qty_void,

            sum((select sum(sub_total) from sales_void sv where sv.product_detail_fkid=sd.product_detail_fkid and sv.sales_fkid=s.sales_id and sv.sales_detail_fkid=sd.sales_detail_id)) as sub_void,
            ifnull(sum((select sum(promotion_value) from sales_detail_promotion sdp where sd.sales_detail_id=sdp.sales_detail_fkid)),0)as promo
		");
		$this->db->from('sales_detail sd');
		$this->db->join('sales s', 'sd.sales_fkid = s.sales_id', 'left');
		$this->db->join('outlets o', 'o.outlet_id = s.outlet_fkid', 'left');
		$this->db->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid', 'left');
		$this->db->join('products p', 'p.product_id = pd.product_fkid', 'left');
		$this->db->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');
		$this->db->join('sales_detail_discount sdc', 'sdc.sales_detail_fkid = sd.sales_detail_id', 'left');
		$this->db->join('products_subcategory ps', 'ps.product_subcategory_id = p.product_subcategory_fkid', 'left');
		$this->db->join('products_category c', 'c.product_category_id = p.product_category_fkid', 'left');

		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id'));
		$this->db->where("date_format(from_unixtime(s.time_created/1000+" . $timeZone . "),'%Y-%m-%d') >=", $startDate);
		$this->db->where("date_format(from_unixtime(s.time_created/1000+" . $timeZone . "),'%Y-%m-%d') <=", $endDate);
		$this->db->where('s.status', 'success');
		$this->db->where('s.data_status', 'on');
		if (!empty($category)) {
			$this->db->where('c.product_category_id', $category);
		}
		if (!empty($outlet)) {
			$this->db->where('o.outlet_id', $outlet);
		}
		$this->db->group_by('pd.product_detail_id');
		$this->db->group_by('o.outlet_id');
		return $this->db->get()->result_array();
	}

	public function getVoid($product_id, $startDate, $endDate, $timeZone)
	{
		$this->db->select("
			sum(sv.qty) as qty_void,
			sum(sv.sub_total) as sub_void
		");
		$this->db->from('sales_void sv');
		$this->db->join('sales s', 's.sales_id = sv.sales_fkid', 'left');
		$this->db->where("date_format(from_unixtime(s.time_created/1000+" . $timeZone . "),'%Y-%m-%d') >=", $startDate);
		$this->db->where("date_format(from_unixtime(s.time_created/1000+" . $timeZone . "),'%Y-%m-%d') <=", $endDate);
		$this->db->where('sv.product_detail_fkid', $product_id);
		return $this->db->get()->row();
	}

	public function data_cogs_v2($startDate, $endDate, $timeZone, $category, $outlet)
	{
		$millisStartDate = unix_time($startDate, $timeZone); 
		$millisEndDate = unix_time($endDate, $timeZone); 
		// file_put_contents("php://stderr", " startDate:".$startDate.", millisEndDate : ".json_encode($millisEndDate)."  \n");

		$this->db->select("
			ANY_VALUE(o.`name`) as outlet_name,
			p.sku,
			pc.`name` as category,
			pc.`name` as sub_category,
			COALESCE(CONCAT(p.`name`, ' (', pdv.variant_name, ')'), p.name) as product_name,
			pdv.variant_sku,
			sum(sd.qty) as qty,
			sum(sd.price * (sd.qty - COALESCE(sv.qty_total, 0))) as total_price,
			pd.product_detail_id,
			sum(sd.price_buy * (sd.qty - COALESCE(sv.qty_total, 0))) as total_hpp_old,
			sum((sd.price_buy * (sd.qty - COALESCE(sv.qty_total, 0)))) as total_hpp,
			sum(sd.discount - COALESCE(sv.discount_total, 0)) as discount_sd,			
			sum(sddg.discount) as discount_sales, 
			sum(sddg.voucher) as voucher_sales, 
			sum(sddg.promotion) as promo_sales,
			sum(sdt.tax) AS tax,
			sum(sdt.discount_tax) AS discount_tax,	
			sum(sdt.service_tax) AS service_tax,
			sum(sdt.voucher_tax) AS voucher_tax,
			sum(COALESCE(sv.qty_total, 0)) qty_void,
			sum(sdp.promotion_value) as promo
		")
			->from("sales_detail sd")
			->join("sales s", "s.sales_id=sd.sales_fkid")
			->join("products_detail pd", "sd.product_detail_fkid=pd.product_detail_id")
			->join("products p", "pd.product_fkid=p.product_id")
			->join("outlets o", "s.outlet_fkid=o.outlet_id")
			->join("products_category pc", "pc.product_category_id=p.product_category_fkid")
			->join("products_detail_variant pdv", "pd.variant_fkid=pdv.variant_id", "left")
			->join("(select sum(qty) as qty_total, sum(discount) as discount_total,sales_detail_fkid from sales_void group by sales_detail_fkid) sv", "sv.sales_detail_fkid = sd.sales_detail_id", "left")
			// ->join("sales_detail_discount sdd","sdd.sales_detail_fkid=sd.sales_detail_id", "left")
			// ->join("sales_detail_tax sdt","sdt.sales_detail_fkid=sd.sales_detail_id", "left")
			->join("(
			select sales_detail_fkid, 
			sum(if(sddi.`type` = 'discount', sddi.total, 0)) as discount,
			sum(if(sddi.`type` = 'voucher', sddi.total, 0)) as voucher,  
			sum(if(sddi.`type` = 'promotion', sddi.total, 0)) as promotion
			from sales_detail_discount  sddi
			group by sales_detail_fkid
			) sddg", "sddg.sales_detail_fkid=sd.sales_detail_id", "left")
			->join("(select sales_detail_fkid,
	sum( if(sdt.category = 'tax', `sdt`.`total`, 0)) AS tax,
	sum( if(sdt.category = 'discount', `sdt`.`total`, 0)) AS discount_tax,
	sum( if(sdt.category = 'service', `sdt`.`total`, 0)) AS service_tax,
	sum( if(sdt.category = 'voucher', `sdt`.`total`, 0)) AS voucher_tax
	from sales_detail_tax sdt
	group by sales_detail_fkid) sdt", "sdt.sales_detail_fkid=sd.sales_detail_id", "left")
			->join("sales_detail_promotion sdp", "sdp.sales_detail_fkid=sd.sales_detail_id", "left")
			->where("p.admin_fkid", $this->session->userdata('admin_id'))
			->where("s.status", "Success")
			->where("s.data_status", "on")
			->where ("s.time_created >= ", $millisStartDate['start'] * 1000)
			->where ("s.time_created <= ", $millisEndDate['end'] * 1000);
			// ->where("FROM_UNIXTIME(s.time_created/1000 + " . $timeZone . ", '%Y-%m-%d') >=", $startDate)
			// ->where("FROM_UNIXTIME(s.time_created/1000 + " . $timeZone . ", '%Y-%m-%d') <=", $endDate);

		if (!empty($category)) {
			$this->db->where('pc.product_category_id', $category);
		}
		if (!empty($outlet)) {
			$this->db->where('o.outlet_id', $outlet);
		}
		$this->db->group_by('pd.product_detail_id');

		$result = $this->db->get()->result_array();		
		file_put_contents("php://stderr", basename(__FILE__).":".__LINE__." >> query: ". preg_replace("/[\n\r\t]/", " ", $this->db->last_query())."\n");
		return $result;
	}

	public function data_cogs($startDate, $endDate, $timeZone, $category, $outlet)
	{
		$this->db->select("
			max(o.name) as outlet_name,
			ifnull(max(p.sku),'-') as sku,
			max(c.name) as category,
			max((select products_subcategory.name from products_subcategory where product_subcategory_id=p.product_subcategory_fkid)) as sub_category,
			concat(max(p.name),' ',ifnull((select variant_name from products_detail_variant where variant_id=pd.variant_fkid),'')) product_name,
			ifnull((select variant_sku from products_detail_variant where variant_id=pd.variant_fkid),'') as variant_sku,
			sum(sd.qty) as qty,
			ROUND(AVG(case when date_format(from_unixtime(sd.time_created/1000+" . $timeZone . "),'%Y-%m-%d') >='" . $startDate . "' && date_format(from_unixtime(sd.time_created/1000+" . $timeZone . "),'%Y-%m-%d') <='" . $endDate . "' then sd.price end),0) as price,
			max(sd.product_detail_fkid) as product_detail_id,
			max(sd.price_buy) as hpp,
			sum(sd.discount) as discout_sd,
			ROUND(AVG(case when date_format(from_unixtime(sd.time_created/1000+" . $timeZone . "),'%Y-%m-%d') >='" . $startDate . "' && date_format(from_unixtime(sd.time_created/1000+" . $timeZone . "),'%Y-%m-%d') <='" . $endDate . "' then sd.sub_total end),0) as sub_total,
			ifnull(sum((select sum(total) from sales_detail_discount where sales_detail_fkid=sd.sales_detail_id)),0) as discount_sales,
			ifnull(sum((select sum(total) from sales_detail_tax where sales_detail_fkid=sd.sales_detail_id and category = 'tax')),0) as tax,
			ifnull(sum((select sum(total) from sales_detail_tax where sales_detail_fkid=sd.sales_detail_id and category = 'discount')),0) as discount_tax,
			ifnull(sum((select sum(total) from sales_detail_tax where sales_detail_fkid=sd.sales_detail_id and category = 'service')),0) as service_tax,
			ifnull(sum((select sum(total) from sales_detail_tax where sales_detail_fkid=sd.sales_detail_id and category = 'voucher')),0) as voucher_tax,
			ifnull(sum((select sum(sub_total) from sales_void where sales_detail_fkid=sd.sales_detail_id)),0) as total_void,
			ifnull(sum((select sum(qty) from sales_void where sales_detail_fkid=sd.sales_detail_id)),0) as qty_void,
			ifnull(sum((select sum(promotion_value) from sales_detail_promotion sdp where sd.sales_detail_id=sdp.sales_detail_fkid)),0)as promo
		")
			->from('sales_detail sd')
			->join('sales s', 's.sales_id=sd.sales_fkid', 'left')
			->join('products p', 'p.product_id=sd.product_fkid', 'left')
			->join('products_detail pd', 'pd.product_detail_id = sd.product_detail_fkid', 'left')
			->join('outlets o', 'o.outlet_id = s.outlet_fkid', 'left')
			->join('products_category c', 'c.product_category_id = p.product_category_fkid', 'left')
			->where('o.admin_fkid', $this->session->userdata('admin_id'))
			->where("date_format(from_unixtime(s.time_created/1000+" . $timeZone . "),'%Y-%m-%d') >=", $startDate)
			->where("date_format(from_unixtime(s.time_created/1000+" . $timeZone . "),'%Y-%m-%d') <=", $endDate)
			->where('s.status', 'success')
			->where('s.data_status', 'on');
		if (!empty($category)) {
			$this->db->where('c.product_category_id', $category);
		}
		if (!empty($outlet)) {
			$this->db->where('o.outlet_id', $outlet);
		}
		$this->db->group_by('pd.product_detail_id');
		// $this->db->group_by('o.outlet_id');

		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", "query: " . $this->db->last_query() . "\n");
		return $result;
	}

	public function data_cogs_v3($startDate, $endDate, $timeZone, $category, $outlet)
	{
		$millisStartDate = unix_time($startDate, $timeZone);
		$millisEndDate = unix_time($endDate, $timeZone);
		// file_put_contents("php://stderr", " startDate:".$startDate.", millisEndDate : ".json_encode($millisEndDate)."  \n");

		// Main query without sales_detail_tax join for better performance
		$this->db->select("
			ANY_VALUE(o.`name`) as outlet_name,
			p.sku,
			pc.`name` as category,
			pc.`name` as sub_category,
			COALESCE(CONCAT(p.`name`, ' (', pdv.variant_name, ')'), p.name) as product_name,
			pdv.variant_sku,
			sum(sd.qty) as qty,
			sum(sd.price * (sd.qty - COALESCE(sv.qty_total, 0))) as total_price,
			pd.product_detail_id,
			sum(sd.price_buy * (sd.qty - COALESCE(sv.qty_total, 0))) as total_hpp_old,
			sum((sd.price_buy * (sd.qty - COALESCE(sv.qty_total, 0)))) as total_hpp,
			sum(sd.discount - COALESCE(sv.discount_total, 0)) as discount_sd,
			sum(sddg.discount) as discount_sales,
			sum(sddg.voucher) as voucher_sales,
			sum(sddg.promotion) as promo_sales,
			sum(COALESCE(sv.qty_total, 0)) qty_void,
			sum(sdp.promotion_value) as promo
		")
			->from("sales_detail sd")
			->join("sales s", "s.sales_id=sd.sales_fkid")
			->join("products_detail pd", "sd.product_detail_fkid=pd.product_detail_id")
			->join("products p", "pd.product_fkid=p.product_id")
			->join("outlets o", "s.outlet_fkid=o.outlet_id")
			->join("products_category pc", "pc.product_category_id=p.product_category_fkid")
			->join("products_detail_variant pdv", "pd.variant_fkid=pdv.variant_id", "left")
			->join("(select sum(qty) as qty_total, sum(discount) as discount_total,sales_detail_fkid from sales_void group by sales_detail_fkid) sv", "sv.sales_detail_fkid = sd.sales_detail_id", "left")
			->join("(select sales_detail_fkid,
	sum( if(sddi.type = 'discount', `sddi`.`total`, 0)) AS discount,
	sum( if(sddi.type = 'voucher', `sddi`.`total`, 0)) AS voucher,
	sum( if(sddi.type = 'promotion', `sddi`.`total`, 0)) AS promotion
	from sales_detail_discount  sddi
	group by sales_detail_fkid
	) sddg", "sddg.sales_detail_fkid=sd.sales_detail_id", "left")
			// Removed the sales_detail_tax join for performance improvement
			->join("sales_detail_promotion sdp", "sdp.sales_detail_fkid=sd.sales_detail_id", "left")
			->where("p.admin_fkid", $this->session->userdata('admin_id'))
			->where("s.status", "Success")
			->where("s.data_status", "on")
			->where ("s.time_created >= ", $millisStartDate['start'] * 1000)
			->where ("s.time_created <= ", $millisEndDate['end'] * 1000);
			// ->where("FROM_UNIXTIME(s.time_created/1000 + " . $timeZone . ", '%Y-%m-%d') >=", $startDate)
			// ->where("FROM_UNIXTIME(s.time_created/1000 + " . $timeZone . ", '%Y-%m-%d') <=", $endDate);

		if (!empty($category)) {
			$this->db->where("pc.product_category_id", $category);
		}
		if (!empty($outlet)) {
			$this->db->where("o.outlet_id", $outlet);
		}

		$this->db->group_by("pd.product_detail_id");
		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", "query v3 main: " . $this->db->last_query() . "\n");
		return $result;
	}

	/**
	 * Get sales detail tax data separately for v3 performance optimization
	 * @param array $productDetailIds Array of product detail IDs
	 * @param string $startDate Start date
	 * @param string $endDate End date
	 * @param string $timeZone Timezone offset
	 * @param string $outlet Outlet ID filter
	 * @return array Tax data grouped by product_detail_fkid
	 */
	public function get_sales_detail_tax_v3($productDetailIds, $startDate, $endDate, $timeZone, $outlet)
	{
		if (empty($productDetailIds)) {
			return array();
		}

		$millisStartDate = unix_time($startDate, $timeZone);
		$millisEndDate = unix_time($endDate, $timeZone);

		$this->db->select("
			sd.product_detail_fkid,
			sum( if(sdt.category = 'tax', `sdt`.`total`, 0)) AS tax,
			sum( if(sdt.category = 'discount', `sdt`.`total`, 0)) AS discount_tax,
			sum( if(sdt.category = 'service', `sdt`.`total`, 0)) AS service_tax,
			sum( if(sdt.category = 'voucher', `sdt`.`total`, 0)) AS voucher_tax
		")
			->from("sales_detail_tax sdt")
			->join("sales_detail sd", "sd.sales_detail_id=sdt.sales_detail_fkid")
			->join("sales s", "s.sales_id=sd.sales_fkid")
			->where_in("sd.product_detail_fkid", $productDetailIds)
			->where("s.status", "Success")
			->where("s.data_status", "on")
			->where("s.time_created >= ", $millisStartDate['start'] * 1000)
			->where("s.time_created <= ", $millisEndDate['end'] * 1000);

		// if (!empty($outlet)) {
		// 	$this->db->where("s.outlet_fkid", $outlet);
		// }

		$this->db->group_by("sd.product_detail_fkid");
		$result = $this->db->get()->result_array();
		file_put_contents("php://stderr", "query v3 tax: " . $this->db->last_query() . "\n");

		// Convert to associative array for easier merging
		$taxData = array();
		foreach ($result as $row) {
			$taxData[$row['product_detail_fkid']] = $row;
		}

		return $taxData;
	}

	public function purchase($startDate, $endDate, $timeZone)
	{
		$data = $this->db->select('ifnull(avg(price_stok),0) as price, ifnull(avg(qty_stok),0) as qty,products_fkid as pd_id')
			->from('purchase_products pp')
			->join('purchase p', 'pp.purchase_fkid=p.purchase_id', 'left')
			->where("date_format(from_unixtime(p.data_created/1000+" . $timeZone . "),'%Y-%m-%d') >=", $startDate)
			->where("date_format(from_unixtime(p.data_created/1000+" . $timeZone . "),'%Y-%m-%d') <=", $endDate)
			->group_by('products_fkid')
			->get()->result_array();
		return $data;
	}

	/**
	 * COGS Detail
	 */
	public function get_cogs_detail($data)
	{
		$millisStartDate = unix_time($data['startDate'], $data['timeZone']); 
		$millisEndDate = unix_time($data['endDate'], $data['timeZone']); 

		$this->db->select("
			ANY_VALUE(o.name) AS outlet_name,
			p.sku,
			s.display_nota,
			c.name AS category,
			c.name AS sub_category,
			sd.price_buy as hpp,
			sd.price,
			FROM_UNIXTIME(
					s.time_created / 1000 + {$data['timeZone']},
					'%d-%m-%Y'
			) AS time_created,
			COALESCE(
				CONCAT(
					p.name,
					' (',
					pdv.variant_name,
					') '
				),
				p.name
			) AS product_name,
			pdv.variant_sku,
			SUM(sd.qty) AS qty,
			SUM(
				sd.price * (
					sd.qty - COALESCE(sv.qty_total, 0)
				)
			) AS total_price,
			pd.product_detail_id,
			SUM(
        sd.price_buy * (
            sd.qty - COALESCE(sv.qty_total, 0)
        )
			) AS total_hpp_old,
			SUM(
        sd.discount - COALESCE(sv.discount_total, 0)
			) AS discount_sd,
			SUM(sddg.discount) As discount_sales,
			SUM(sddg.voucher) AS voucher_sales,
			SUM(sddg.promotion) AS promo_sales,
			SUM(sdt.tax) AS tax,
			SUM(sdt.discount_tax) AS discount_tax,
			SUM(sdt.service_tax) As service_tax,
			SUM(sdt.voucher_tax) AS voucher_tax,
			SUM(COALESCE(sv.qty_total, 0)) AS qty_void,
			SUM(sdp.promotion_value) AS promo
		");
		$this->db->from("sales_detail sd");
		$this->db->join("sales s", "s.sales_id=sd.sales_fkid");
		$this->db->join("products_detail pd", "pd.product_detail_id=sd.product_detail_fkid");
		$this->db->join("products p", "p.product_id=pd.product_fkid");
		$this->db->join("outlets o", "o.outlet_id=s.outlet_fkid");
		$this->db->join("products_category c", "c.product_category_id=p.product_category_fkid");
		$this->db->join("products_detail_variant pdv", "pdv.variant_id=pd.variant_fkid", "LEFT");
		$this->db->join("(
			SELECT
					SUM(qty) AS qty_total,
					SUM(discount) AS discount_total,
					sales_detail_fkid
			FROM sales_void
			GROUP BY
					sales_detail_fkid
		) AS sv", "sv.sales_detail_fkid=sd.sales_detail_id", "LEFT");
		$this->db->join('(
			SELECT
					sales_detail_fkid,
					SUM(
							IF(
									sddi.type = "discount",
									sddi.total,
									0
							)
					) AS discount,
					SUM(
							IF(
									sddi.type = "voucher",
									sddi.total,
									0
							)
					) As voucher,
					SUM(
							IF(
									sddi.type = "promotion",
									sddi.total,
									0
							)
					) AS promotion
			FROM
					sales_detail_discount sddi
			GROUP BY
					sales_detail_fkid
		) AS sddg', "sddg.sales_detail_fkid=sd.sales_detail_id", "LEFT");
		$this->db->join('(
			SELECT
					sales_detail_fkid,
					SUM(
							IF(
									sdt.category = "tax",
									sdt.total,
									0
							)
					) AS tax,
					SUM(
							IF(
									sdt.category = "discount",
									sdt.total,
									0
							)
					) AS discount_tax,
					SUM(
							IF(
									sdt.category = "service",
									sdt.total,
									0
							)
					) AS service_tax,
					SUM(
							IF(
									sdt.category = "voucher",
									sdt.total,
									0
							)
					) AS voucher_tax
			FROM
					sales_detail_tax sdt
			GROUP BY
					sales_detail_fkid
		) AS sdt', "sdt.sales_detail_fkid=sd.sales_detail_id", "LEFT");
		$this->db->join("sales_detail_promotion sdp", "sdp.sales_detail_fkid=sd.sales_detail_id", "LEFT");
		$this->db->where("o.admin_fkid", $this->session->userdata("admin_id"));
		$this->db->where("s.status", "success");
		$this->db->where("s.data_status", "on");
		$this->db->where("pd.product_detail_id", $data['product_detail_id']);
		$this->db->where("s.time_created >= ", $millisStartDate['start'] * 1000);
		$this->db->where("s.time_created <= ", $millisEndDate['start'] * 1000);
		//#These two queries are slow
		// $this->db->where("FROM_UNIXTIME(
		// 	s.time_created / 1000 + {$data['timeZone']},
		// 	'%Y-%m-%d'
		// ) >=", $data['startDate']);
		// $this->db->where("FROM_UNIXTIME(
		// 	s.time_created / 1000 + {$data['timeZone']},
		// 	'%Y-%m-%d'
		// ) <=", $data['endDate']);
		$this->db->group_by("sd.sales_detail_id");

		return $this->db->get();
	}
}

/* End of file M_report_cogs.php */
/* Location: ./application/modules/cogs/models/M_report_cogs.php */
