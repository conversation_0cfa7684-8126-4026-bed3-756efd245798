<?php
defined('BASEPATH') or exit('No direct script access allowed');

use Mpdf\Mpdf;

class History extends Auth_Controller
{

	public function __construct()
	{
		parent::__construct();
		//proteksi halaman
		// set_time_limit(1000);
		// ini_set('memory_limit','1600M');
		$this->load->model('outlet/Outlets_model', 'Unit_model');
		$this->load->model('outlet/Shift_model', 'Shift_model');
		$this->load->model('outlet/Outlets_model', 'Outlets_model');
		$this->load->model('report/sales/Report_sales_history_model', 'history_model');
		$this->api_report_endpoint = getenv('SERVICE_REPORT');
	}

	public function index()
	{
		$promotion_id = "";
		$nomor_nota = "";
		$member_id = "";
		$province = "";
		$gender_id = "";
		$outlets = "";
		$membertype = "";
		$tab = "";
		$customer_name = "";
		$customer_phone = "";
		if ($this->input->get("tab") != "") {
			$tab = $this->input->get("tab");
		}
		if ($this->input->get("promotion-id") != "") {
			$promotion_id = $this->input->get("promotion-id");
		} else if ($this->input->get("id") != "") {
			$nomor_nota = $this->input->get("id");
		} else if ($this->input->get("member_id") != "") {
			$member_id = $this->input->get("member_id");
		} else if ($this->input->get("gender") != "" || $this->input->get("province") != "" || $this->input->get("membertype") != "" || $this->input->get("outlets") != "") {
			$gender_id = $this->input->get("gender");
			$province = $this->input->get("province");
			$membertype = $this->input->get("membertype");
			$outlets = $this->input->get("outlets");
		} else if ($this->input->get("costumer") != "") {
			$costumer_name = $this->input->get("costumer");
		}


		if ($this->input->get("customer") != "") {
			$customer_name = str_replace("+", " ", urlencode($this->input->get("customer")));
			$customer_phone = $this->input->get("contact");
		}

		$link = current_url() . '/';

		$data = array(
			'currentUser' => $this->session->userdata['user_name'],
			'ajaxActionDatatableAll' => $link . 'jsonAll/',
			'ajaxActionDatatableSuccsess' => $link . 'jsonSuccsess/',
			'ajaxActionDatatableRefund' => $link . 'jsonRefund/',
			'ajaxActionDatatableDetail' => $link . 'jsonDetail/',
			'ajaxActionDatatableVoid' => $link . 'jsonVoid/',

		);


		$els = '';
		// $data['form_select_outlet'] = $this->Outlets_model->form_select();
		// outlet employee by employee
		$data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
		$data['form_select_shift'] = $this->Shift_model->form_select();
		$data['promotion_id'] = $promotion_id;
		$data['nomor_nota'] = $nomor_nota;
		$data['member_id'] = $member_id;
		$data['gender'] = $gender_id;
		$data['province'] = $province;
		$data['membertype'] = $membertype;
		$data['outlets'] = $outlets;
		$data['tab'] = $tab;
		$data['customer'] = $customer_name;
		$data['contact'] = $customer_phone;


		$current_version = '3'; //ready production version
		$version = $this->input->get('v');


		if (!isset($version)) {
			$version = $current_version;
		}

		//default
		$data['ajaxActionDatatableAll'] = $this->api_report_endpoint . 'v1/sales/transaction';

		if ($version == '2') {
			$data['ajaxActionDatatableDetail'] = $link . 'jsonDetailV2/';
			// $data['ajaxActionDatatableAll'] = $link . 'jsonAllV2/';
			$data['ajaxActionDatatableAll'] = $this->api_report_endpoint . 'v1/sales/transaction';
			// $data['ajaxActionDatatableAll'] = $this->api_report_endpoint.'v2/sales/transaction';
		} else if ($version == '3') {
			$data['ajaxActionDatatableAll'] = $this->api_report_endpoint . 'v2/sales/transaction';
			$data['ajaxActionDatatableDetail'] = $this->api_report_endpoint . 'v1/sales/transaction-detail';
		}

		//template v1
		// $this->load->view('reports/sales/sales_history/sales_history_v',$data);
		$env = "development";
		if (!empty($_ENV["CI_ENV"])) {
			$env = $_ENV["CI_ENV"];
		}

		$promoId = $this->input->get('promotion-id');
		file_put_contents("php://stderr", "promoId --> " . $promoId . " | env: $env \n");
		if (isset($promoId)) {
			$data['ajaxActionDatatableAll'] = $this->api_report_endpoint . 'v1/sales/transaction';
		}


		$details = $this->input->get('id');
		file_put_contents("php://stderr", "details --> " . $details . "\n");
		if (isset($details)) {
			$data['ajaxActionDatatableDetail']  = $link . 'jsonFromDetail';
		}

		$member = $this->input->get('member_id');
		file_put_contents("php://stderr", "member --> " . $member . "\n");
		if (isset($member)) {
			$data['ajaxActionDatatableDetail']  = $link . 'JsonFromMember';
		}

		$group = $this->input->get('gender');
		file_put_contents("php://stderr", "gender --> " . $group . "\n");
		if (isset($group)) {
			$data['ajaxActionDatatableDetail']  = $link . 'JsonFromGroup';
		}
		$province = $this->input->get('province');
		file_put_contents("php://stderr", "province --> " . $province . "\n");
		if (isset($province)) {
			$data['ajaxActionDatatableDetail']  = $link . 'JsonFromGroup';
		}
		$membertype = $this->input->get('membertype');
		file_put_contents("php://stderr", "membertype --> " . $membertype . "\n");
		if (isset($membertype)) {
			$data['ajaxActionDatatableDetail']  = $link . 'JsonFromGroup';
		}
		$outlets = $this->input->get('outlets');
		file_put_contents("php://stderr", "outlets --> " . $outlets . "\n");
		if (isset($outlets)) {
			$data['ajaxActionDatatableDetail']  = $link . 'JsonFromGroup';
		}





		//template v2

		$data['page_title'] = 'Sales History Report';
		$this->template->view('reports/sales/sales_history/sales_history_v2', $data);
	}

	public function jsonAll()
	{
		header('Content-Type: application/json');
		file_put_contents("php://stderr", "return empty... using old api version\n");
		$max = 50; //maximum limit

		//limit the result.. to also prevent bot
		if ($this->input->post('limit') > $max) {
			file_put_contents("php://stderr", "return empty... because limit is --> " . $this->input->post('limit') . "\n");
			$draw_json = array(
				'data' => [],
				"draw" => $this->input->post('draw')
			);
			return $this->response_json($draw_json);
		}

		$limit = $max;
		$offset = $max * $this->input->post('page');

		$param = [
			"shift" => filter_shift($this->input->post('shift')),
			"outlet" => outlet_employee($this->input->post('outlet')),
			"timeZone" => $this->input->post('timeZone'),
			"startDate" => $this->input->post('startDate'),
			"endDate" => $this->input->post('endDate'),
			"dataType" => $this->input->post('dataType'),
			"limit" => $limit,
			"offset" => $offset,
			"dataStatus" => $this->input->post('dataStatus'),
			"offsetId" => 0,
			// "nomor_nota" => $this->input->post('nomor_nota')
		];

		return $this->jsonHistory($param);
	}

	//FUNGSI JSON BY SALES
	public function jsonAllV2()
	{
		header('Content-Type: application/json');
		$max = 50; //maximum limit

		//limit the result.. to also prevent bot
		if ($this->input->post('limit') > $max) {
			file_put_contents("php://stderr", "return empty... because limit is --> " . $this->input->post('limit') . "\n");
			$draw_json = array(
				'data' => [],
				"draw" => $this->input->post('draw')
			);
			return $this->response_json($draw_json);
		}


		$limit = $max;
		$offset = $max * $this->input->post('page');

		$param = [
			"shift" => filter_shift($this->input->post('shift')),
			"outlet" => outlet_employee($this->input->post('outlet')),
			"timeZone" => $this->input->post('timeZone'),
			"startDate" => $this->input->post('startDate'),
			"endDate" => $this->input->post('endDate'),
			"dataType" => $this->input->post('dataType'),
			"limit" => $limit,
			"offset" => $offset,
			"dataStatus" => $this->input->post('dataStatus'),
			"promotion_id" => $this->input->post("promotion_id"),
			"customer_name" => $this->input->post("customer"),
			"contact" => $this->input->post("contact"),
		];

		$offsetId = $this->input->post('offsetId');
		$parm['offsetId'] = $offsetId;
		// if (isset($offsetId) && $offsetId > 0) {
		// 	$parm['offset'] = 0;
		// }		

		return $this->jsonHistory($param);
	}

	public function jsonHistory($param)
	{
		$jsondata = $this->history_model->jsonTransaksi($param); //jsonTransaksiV2 or jsonTransaksi

		// echo $this->db->last_query();
		// die();

		$dataArray = [];

		$salesIds = [];
		foreach ($jsondata as $sales) {
			array_push($salesIds, $sales['nomor_nota']);
		}

		file_put_contents("php://stderr", "total ids --> " . count($salesIds) . "\n");
		$payments = [];
		if (count($salesIds) > 0) {
			$payments = $this->history_model->getPayments($salesIds);
			file_put_contents("php://stderr", "total payments --> " . count($payments) . "\n");
		}

		$offsetId = 0;
		foreach ($jsondata as $a) {
			//perhitungan
			$total_discount = $a['discount_sales'] + $a['discountSd'] + $a['discountTax'];
			$total_voucher = $a['voucherTax'] + $a['voucherSales'];

			$display_nota = $a['display_nota'];
			if (empty($display_nota)) {
				$display_nota = $a['nomor_nota'];
			}

			$payment = [];
			$found = False;
			foreach ($payments as $p) {
				// if already found all, break loop
				if ($found && $p['sales_fkid'] != $a['nomor_nota']) {
					break;
				}

				if ($p['sales_fkid'] == $a['nomor_nota']) {
					$payment[$p['method']] = $p['pay'];
					$found = True;
				}
			}

			if ($a['time_created'] > $offsetId) {
				$offsetId = $a['time_created'];
			}

			if ($a['promo_voucher']) {
				$a['promo_info'] = $a['promo_info'] . ' (' . $a['promo_voucher'] . ')';
			}

			$temp_data = array(
				'tanggal' =>  millis_to_localtime('d-m-Y H:i:s', $a['tanggal']),
				'nomor_nota' => $display_nota,
				'outlet_name' => $a['outlet_name'],
				'shift_name' => $a['shift_name'],
				'pembayaran' => $payment,
				'pax' => $a['pax'],
				'sub_total' => $a['sub_total'] + $a['sub_void'],
				'discountSales' => $a['discountSales'],
				'discountSd' => $a['discountSd'],
				'discountTax' => $a['discountTax'],
				'keterangan_discount' => $a['keterangan_discountS'] . ',' . $a['keterangan_discountSd'],
				'keterangan_voucherS' => $a['keterangan_voucherS'],
				'tax' => $a['tax'],
				'service' => $a['service'],
				'grand_total' => $a['grand_total'],
				'status' => $a['status'],
				'total_discount' => $total_discount,
				'total_voucher' => $total_voucher,
				'employee' => $a['employee'],
				'sub_void' => $a['sub_void'],
				'promo' => $a['promo'],
				'keterangan_promo' => $a['promo_info']
			);
			array_push($dataArray, $temp_data);
		}

		$draw_json = array(
			'data' => $dataArray,
			"draw" => $this->input->post('draw'),
			'offset_id' => $offsetId,
		);
		file_put_contents("php://stderr", "finish..... getting sales history\n");
		return $this->response_json($draw_json);
	}

	//json where succsess
	public function jsonSuccsess($shift, $outlet, $timeZone, $startDate, $endDate, $dataType)
	{
		header('Content-Type: application/json');
		$jsondata = $this->history_model->jsonSuccsessV2($shift, $outlet, $timeZone, $startDate, $endDate, $dataType);
		$dataArray = array();
		foreach ($jsondata as $a) {

			//perhitungan
			$total_discount = $a['discount_sales'] + $a['discountSd'] + $a['discountTax'];
			$total_voucher = $a['voucherTax'] + $a['voucherSales'];
			$grand_total = $a['sub_total'] + $a['tax'] + $a['service'] - $total_voucher;

			$display_nota = $a['display_nota'];
			if (empty($display_nota)) {
				$display_nota = $a['nomor_nota'];
			}

			$dataPayment = $this->history_model->getPayment($a['nomor_nota']);
			if (count($dataPayment) > 1) {
				if (count($dataPayment) <= 2 && $dataPayment[1]['method'] == "CARD") {
					$tmpData = $this->history_model->getPayDetail($dataPayment[1]['payment_id']);
					$payment = array(
						$dataPayment[0]['method'] => $dataPayment[0]['pay'],
						$tmpData->name => $tmpData->pay
					);
				} else if (count($dataPayment) <= 2 && $dataPayment[1]['method'] != "CARD") {
					$payment = array(
						$dataPayment[0]['method'] => $dataPayment[0]['pay'],
						$dataPayment[1]['method'] => $dataPayment[1]['pay'],
					);
				} else {
					$tmpData = $this->history_model->getPayDetail($dataPayment[1]['payment_id']);
					$payment = array(
						$dataPayment[0]['method'] => $dataPayment[0]['pay'],
						$dataPayment[2]['method'] => $dataPayment[2]['pay'] * -1,
					);
					if ($tmpData) {
						$payment[$tmpData->name] = $tmpData->pay;
					}
				}
			} else {
				$payment = array($dataPayment[0]['method'] => $dataPayment[0]['pay']);
			}

			$temp_data = array(
				'tanggal' =>  millis_to_localtime('Y-m-d H:i:s', $a['tanggal']),
				'nomor_nota' => $display_nota,
				'outlet_name' => $a['outlet_name'],
				'shift_name' => $a['shift_name'],
				'pembayaran' => $payment,
				'pax' => formatAngka($a['pax']),
				'sub_total' => formatUang($a['sub_total'] + $a['sub_void']),
				'discountSales' => formatUang($a['discountSales']),
				'discountSd' => formatUang($a['discountSd']),
				'discountTax' => formatUang($a['discountTax']),
				'keterangan_discount' => $a['keterangan_discountS'] . ',' . $a['keterangan_discountSd'],
				'keterangan_voucherS' => $a['keterangan_voucherS'],
				'tax' => formatUang($a['tax']),
				'service' => formatUang($a['service']),
				'grand_total' => formatUang($a['grand_total']),
				'status' => $a['status'],
				'total_discount' => formatUang($total_discount),
				'total_voucher' => formatUang($total_voucher),
				'employee' => $a['employee'],
				'sub_void' => $a['sub_void']

			);
			array_push($dataArray, $temp_data);
		}

		//remake json data (JANGAN DIEDIT)
		$draw_json = array(
			'data' => $dataArray
		);

		//tampilkan data json
		echo json_encode($draw_json);
		/* custom json output  end */
	}


	//FUNGSI TRANSAKSI REFUND
	public function jsonRefund($shift, $outlet, $timeZone, $startDate, $endDate, $dataType)
	{
		header('Content-Type: application/json');
		$jsondata = $this->history_model->jsonRefundV2($shift, $outlet, $timeZone, $startDate, $endDate, $dataType);
		// echo $this->db->last_query();die();
		// print_r($jsondata);die();
		$dataArray = array();
		foreach ($jsondata as $a) {

			//perhitungan
			$total_discount = $a['discount_sales'] + $a['discountSd'] + $a['discountTax'];
			$total_voucher = $a['voucherTax'] + $a['voucherSales'];
			$grand_total = $a['sub_total'] + $a['tax'] + $a['service'] - $total_voucher;


			$display_nota = $a['display_nota'];
			if (empty($display_nota)) {
				$display_nota = $a['nomor_nota'];
			}

			$dataPayment = $this->history_model->getPayment($a['nomor_nota']);
			$payment = [];
			if ($dataPayment) {
				foreach ($dataPayment as $key) {
					$payment[$key['method']] = $key['pay'];
					if ($key['method'] == "CARD") {
						unset($payment['CARD']);
						$tmpData = $this->history_model->getPayDetail($key['payment_id']);
						$payment[$tmpData->name] = $tmpData->pay;
					}
				}
			}


			$temp_data = array(
				'tanggal' =>  millis_to_localtime('Y-m-d H:i:s', $a['tanggal']),
				'nomor_nota' => $display_nota,
				'outlet_name' => $a['outlet_name'],
				'shift_name' => $a['shift_name'],
				'pembayaran' => $payment,
				'pax' => formatAngka($a['pax']),
				'sub_total' => formatUang($a['sub_total'] + $a['sub_void']),
				'discountSales' => formatUang($a['discountSales']),
				'discountSd' => formatUang($a['discountSd']),
				'discountTax' => formatUang($a['discountTax']),
				'keterangan_discount' => $a['keterangan_discountS'] . ',' . $a['keterangan_discountSd'],
				'keterangan_voucherS' => $a['keterangan_voucherS'],
				'tax' => formatUang($a['tax']),
				'service' => formatUang($a['service']),
				'grand_total' => formatUang($a['grand_total']),
				'status' => $a['status'],
				'total_discount' => formatUang($total_discount),
				'total_voucher' => formatUang($total_voucher),
				'employee' => $a['employee'],
				'ket_refund' => $a['ket_refund']

			);
			array_push($dataArray, $temp_data);
		}

		//remake json data (JANGAN DIEDIT)
		$draw_json = array(
			'data' => $dataArray
		);

		//tampilkan data json
		echo json_encode($draw_json);
		/* custom json output  end */
	}


	public function jsonDetailV2()
	{
		header('Content-Type: application/json');

		$param = [
			"shift" => filter_shift($this->input->post('shift')),
			"outlet" => outlet_employee($this->input->post('outlet')),
			"timeZone" => $this->input->post('timeZone'),
			"startDate" => $this->input->post('startDate'),
			"endDate" => $this->input->post('endDate'),
			"dataType" => $this->input->post('dataType'),
			"limit" => $this->input->post('limit'),
			"offset" => $this->input->post('offset'),
			"offsetId" => $this->input->post('offsetId'),
			"display_nota" => $this->input->post('display_nota'),

		];

		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " -----> sales_id: " . $this->input->get('sales-id') . " \n");
		$result = $this->history_model->jsonDetailV2($param);

		$salesIds = [];
		$offsetId = 0;
		for ($i = 0; $i < count($result); $i++) { //$result as &$sales
			$sales = &$result[$i];
			$sales['qty'] = $sales['qty'] - (int)$sales['qty_void'];
			$sales['total_voucher'] = (int)$sales['voucher_sales'] + (int)$sales['voucher_tax'];
			$sales['total_discount'] = (int)$sales['disc_sales'] + (int)$sales['disc_tax'] + (int)$sales['discount'];
			$sales['sub_total'] = $sales['qty'] * $sales['price'];
			$sales['promo'] = $sales['promo'] + $sales['promo_item'];
			$sales['category'] = htmlspecialchars($sales['category']);
			$sales['sub_category'] = htmlspecialchars($sales['sub_category']);
			// $sales['grand_total2'] = $sales['grand_total'];
			$sales['grand_total2'] = (int)$sales['sub_total'] + (int)$sales['tax'] + (int)$sales['service'] - (int)$sales['total_discount'] - (int)$sales['promo'] - (int)$sales['total_voucher'];

			array_push($salesIds, $sales['sales_id']);
			if ((int)$sales['sales_detail_id'] > $offsetId) {
				$offsetId = $sales['sales_detail_id'];
			}

			if ($sales['qty'] == 0) {
				// Remove the element from the array
				// unset($sales);
				unset($result[$i]);
				file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " -----> find qty 0, id: " . $sales['sales_detail_id'] . " \n");
			}
		}

		$payments = $this->history_model->getPayments($salesIds);

		foreach ($result as &$sales) {
			$sales_payment = [];
			$found = false;
			foreach ($payments as $p) {
				if ($found && $p['sales_fkd'] != $sales['sales_id']) {
					break;
				}

				if ($p['sales_fkid'] == $sales['sales_id']) {
					$sales_payment[$p['method']] = $p['pay'];
				}
			}

			$sales['pembayaran'] = $sales_payment;
			if ($sales['qty'] == 0) {
				// Remove the element from the array
				// unset($sales);

			}
		}

		$draw_json = array(
			'data' => $result,
			"draw" => 1,
			"recordsFiltered" => count($result),
			"recordsTotal" => count($result),
			'offset_id' => $offsetId,
		);

		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " totalSalesDetail: " . count($result) . " | offsetId-param: " . $param['offsetId'] . " | offsetId: " . $offsetId . " \n");
		return $this->response_json($draw_json);
	}

	public function JsonFromDetail()
	{
		header('Content-Type: application/json');

		$param = [
			"shift" => filter_shift($this->input->post('shift')),
			"outlet" => outlet_employee($this->input->post('outlet')),
			"timeZone" => $this->input->post('25200'),
			"startDate" => $this->input->post('1672678800000'),
			"endDate" => $this->input->post('1673369999999'),
			"dataType" => $this->input->post('dataType'),
			"limit" => $this->input->post('limit'),
			"offset" => $this->input->post('offset'),
			"offsetId" => $this->input->post('offsetId'),
			"nomor_nota" => $this->input->post('nomor_nota'),
			// "id" => "OM202212281",			
		];

		$result = $this->history_model->JsonFromDetail($param);

		$salesIds = [];
		$offsetId = 0;
		foreach ($result as &$sales) {
			$sales['qty'] = $sales['qty'] - (int)$sales['qty_void'];
			$sales['total_voucher'] = (int)$sales['voucher_sales'] + (int)$sales['voucher_tax'];
			$sales['total_discount'] = (int)$sales['disc_sales'] + (int)$sales['disc_tax'];
			$sales['sub_total'] = $sales['qty'] * $sales['price'];
			// $sales['grand_total2'] = $sales['grand_total'];
			$sales['grand_total2'] = (int)$sales['sub_total'] + (int)$sales['tax'] + (int)$sales['service'] - (int)$sales['total_discount'] - (int)$sales['promo'] - (int)$sales['total_voucher'];

			array_push($salesIds, $sales['sales_id']);
			if ((int)$sales['sales_detail_id'] > $offsetId) {
				$offsetId = $sales['sales_detail_id'];
			}
		}

		$payments = $this->history_model->getPayments($salesIds);

		foreach ($result as &$sales) {
			$sales_payment = [];
			$found = false;
			foreach ($payments as $p) {
				if ($found && $p['sales_fkd'] != $sales['sales_id']) {
					break;
				}

				if ($p['sales_fkid'] == $sales['sales_id']) {
					$sales_payment[$p['method']] = $p['pay'];
				}
			}

			$sales['pembayaran'] = $sales_payment;
		}

		$draw_json = array(
			'data' => $result,
			"draw" => 1,
			"recordsFiltered" => count($result),
			"recordsTotal" => count($result),
			'offset_id' => $offsetId,
		);

		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " totalSalesDetail: " . count($result) . " | offsetId-param: " . $param['offsetId'] . " | offsetId: " . $offsetId . " \n");
		return $this->response_json($draw_json);
	}

	public function JsonFromMember()
	{
		header('Content-Type: application/json');

		$param = [
			"shift" => filter_shift($this->input->post('shift')),
			"outlet" => outlet_employee($this->input->post('outlet')),
			"timeZone" => $this->input->post('25200'),
			"startDate" => $this->input->post('1672678800000'),
			"endDate" => $this->input->post('1673369999999'),
			"dataType" => $this->input->post('dataType'),
			"limit" => $this->input->post('limit'),
			"offset" => $this->input->post('offset'),
			"offsetId" => $this->input->post('offsetId'),
			"nomor_nota" => $this->input->post('nomor_nota'),
			"member_id" => $this->input->post('member_id'),
			"gender_id" => $this->input->post('gender_id'),
			"province" => $this->input->post('province'),
			// "id" => "OM202212281",

		];



		$result = $this->history_model->JsonFromMember($param);

		$salesIds = [];
		$offsetId = 0;
		foreach ($result as &$sales) {
			$sales['qty'] = $sales['qty'] - (int)$sales['qty_void'];
			$sales['total_voucher'] = (int)$sales['voucher_sales'] + (int)$sales['voucher_tax'];
			$sales['total_discount'] = (int)$sales['disc_sales'] + (int)$sales['disc_tax'];
			$sales['sub_total'] = $sales['qty'] * $sales['price'];
			// $sales['grand_total2'] = $sales['grand_total'];
			$sales['grand_total2'] = (int)$sales['sub_total'] + (int)$sales['tax'] + (int)$sales['service'] - (int)$sales['total_discount'] - (int)$sales['promo'] - (int)$sales['total_voucher'];

			array_push($salesIds, $sales['sales_id']);
			if ((int)$sales['sales_detail_id'] > $offsetId) {
				$offsetId = $sales['sales_detail_id'];
			}
		}

		$payments = $this->history_model->getPayments($salesIds);

		foreach ($result as &$sales) {
			$sales_payment = [];
			$found = false;
			foreach ($payments as $p) {
				if ($found && $p['sales_fkd'] != $sales['sales_id']) {
					break;
				}

				if ($p['sales_fkid'] == $sales['sales_id']) {
					$sales_payment[$p['method']] = $p['pay'];
				}
			}

			$sales['pembayaran'] = $sales_payment;
		}

		$draw_json = array(
			'data' => $result,
			"draw" => 1,
			"recordsFiltered" => count($result),
			"recordsTotal" => count($result),
			'offset_id' => $offsetId,
		);

		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " totalSalesDetail: " . count($result) . " | offsetId-param: " . $param['offsetId'] . " | offsetId: " . $offsetId . " \n");
		return $this->response_json($draw_json);
	}
	public function JsonFromGroup()
	{
		header('Content-Type: application/json');

		$param = [
			"shift" => filter_shift($this->input->post('shift')),
			"outlet" => outlet_employee($this->input->post('outlet')),
			"groupby" => filtergroup_type($this->input->post('groupby')),
			"timeZone" => $this->input->post('25200'),
			"startDate" => $this->input->post('1672678800000'),
			"endDate" => $this->input->post('1673369999999'),
			"dataType" => $this->input->post('dataType'),
			"limit" => $this->input->post('limit'),
			"offset" => $this->input->post('offset'),
			"offsetId" => $this->input->post('offsetId'),
			"nomor_nota" => $this->input->post('nomor_nota'),
			"member_id" => $this->input->post('member_id'),
			"gender" => $this->input->post('gender'),
			"province" => $this->input->post('province'),
			"membertype" => $this->input->post('membertype'),
			"outlets" => $this->input->post('outlets'),

		];



		$result = $this->history_model->JsonFromGroup($param);

		$salesIds = [];
		$offsetId = 0;
		foreach ($result as &$sales) {
			$sales['qty'] = $sales['qty'] - (int)$sales['qty_void'];
			$sales['total_voucher'] = (int)$sales['voucher_sales'] + (int)$sales['voucher_tax'];
			$sales['total_discount'] = (int)$sales['disc_sales'] + (int)$sales['disc_tax'];
			$sales['sub_total'] = $sales['qty'] * $sales['price'];
			// $sales['grand_total2'] = $sales['grand_total'];
			$sales['grand_total2'] = (int)$sales['sub_total'] + (int)$sales['tax'] + (int)$sales['service'] - (int)$sales['total_discount'] - (int)$sales['promo'] - (int)$sales['total_voucher'];

			array_push($salesIds, $sales['sales_id']);
			if ((int)$sales['sales_detail_id'] > $offsetId) {
				$offsetId = $sales['sales_detail_id'];
			}
		}

		$payments = $this->history_model->getPayments($salesIds);

		foreach ($result as &$sales) {
			$sales_payment = [];
			$found = false;
			foreach ($payments as $p) {
				if ($found && $p['sales_fkd'] != $sales['sales_id']) {
					break;
				}

				if ($p['sales_fkid'] == $sales['sales_id']) {
					$sales_payment[$p['method']] = $p['pay'];
				}
			}

			$sales['pembayaran'] = $sales_payment;
		}

		$draw_json = array(
			'data' => $result,
			"draw" => 1,
			"recordsFiltered" => count($result),
			"recordsTotal" => count($result),
			'offset_id' => $offsetId,
		);

		file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " totalSalesDetail: " . count($result) . " | offsetId-param: " . $param['offsetId'] . " | offsetId: " . $offsetId . " \n");
		return $this->response_json($draw_json);
	}

	//FUNGSI JSON DETIL
	public function jsonDetail()
	{
		header('Content-Type: application/json');
		$param = [
			"shift" => filter_shift($this->input->post('shift')),
			"outlet" => outlet_employee($this->input->post('outlet')),
			"timeZone" => $this->input->post('timeZone'),
			"startDate" => $this->input->post('startDate'),
			"endDate" => $this->input->post('endDate'),
			"dataType" => $this->input->post('dataType'),
			"limit" => $this->input->post('limit'),
			"offset" => $this->input->post('offset')
		];
		$jsondata = $this->history_model->jsonDetail($param);
		$dataArray = array();
		foreach ($jsondata as $a) {
			$void = $this->history_model->getvoid_BySalesDetail($a['sales_detail_id'], $a['product_detail_fkid'], $a['sales_id']);
			//perhitungan
			$total_discount = $a['dis_detail'] + $a['discountSd'] + $a['discountTax'];
			$total_voucher = $a['voucherTax'] + $a['vo_detail'];
			$grand_total2 = ($a['sub_total'] + $void->sub_void) + $a['tax'] + $a['service'] - $total_voucher - $total_discount - $a['promo'];
			$employee = $a['employee'];
			$oprator = $a['oprator'];
			if ($oprator == null) {
				$oprator = $employee;
			}

			$display_nota = $a['display_nota'];
			if (empty($display_nota)) {
				$display_nota = $a['nomor_nota'];
			}

			$dataPayment = $this->history_model->getPayment($a['nomor_nota']);
			$payment = [];
			if ($dataPayment) {
				foreach ($dataPayment as $key) {
					$payment[$key['method']] = $key['pay'];
					if ($key['method'] == "CARD") {
						unset($payment['CARD']);
						$tmpData = $this->history_model->getPayDetail($key['payment_id']);
						$payment[$tmpData->name] = $tmpData->pay;
					}
				}
			}


			$temp_data = array(
				'tanggal' =>  millis_to_localtime('d-m-Y H:i:s', $a['tanggal']),
				'nomor_nota' => $display_nota,
				'outlet_name' => $a['outlet_name'],
				'shift_name' => $a['shift_name'],
				'pembayaran' => $payment,
				'pax' => formatAngka($a['pax']),
				'sub_total' => $a['sub_total'] + $void->sub_void,
				'discountSales' => $a['discountSales'],
				'discountSd' => $a['discountSd'],
				'discountTax' => $a['discountTax'],
				'keterangan_discount' => $a['keterangan_discountS'] . ',' . $a['keterangan_discountSd'],
				'keterangan_voucherS' => $a['keterangan_voucherS'],
				'tax' => $a['tax'],
				'service' => $a['service'],
				'grand_total' => $a['grand_total'],
				'status' => $a['status'],
				'sku' => $a['sku'],
				'price' => $a['price'],
				'qty' => formatAngka($a['qty'] - $void->qty_void),
				'product_name' => trim($a['product_name'] . " " . $a['variant']),
				'customer_name' => $a['customer_name'],
				'total_discount' => $total_discount,
				'total_voucher' => $total_voucher,
				'grand_total2' => $grand_total2,
				'employee' => $employee,
				'oprator' => $oprator,
				'qty_void' => $void->qty_void,
				'sub_void' => $void->sub_void,
				'pd_id' => $a['product_detail_fkid'],
				'sd' => $a['sales_detail_id'],
				's' => $a['sales_id'],
				'sub_category' => $a['sub_category'],
				'category' => $a['category'],
				'promo' => $a['promo']
			);
			array_push($dataArray, $temp_data);
		}
		$draw_json = array(
			'data' => $dataArray,
			"draw" => 1,
			"recordsFiltered" => count($dataArray),
			"recordsTotal" => count($dataArray),
			'offset_id' => 0
		);
		return $this->response_json($draw_json);
	}

	//json DATA VOID
	public function jsonVoid()
	{
		header('Content-Type: application/json');
		$param = [
			"shift" => filter_shift($this->input->post('shift')),
			"outlet" => outlet_employee($this->input->post('outlet')),
			"timeZone" => $this->input->post('timeZone'),
			"startDate" => $this->input->post('startDate'),
			"endDate" => $this->input->post('endDate'),
			"dataType" => $this->input->post('dataType'),
			"limit" => $this->input->post('limit'),
			"offset" => $this->input->post('offset'),
		];
		$jsondata = $this->history_model->jsonVoidDetail($param);
		$dataArray = array();
		foreach ($jsondata as $a) {
			$display_nota = $a['display_nota'];
			if (empty($display_nota)) {
				$display_nota = $a['nomor_nota'];
			}
			$product_name = $a['product_name'];
			if ($a['variant'] != '') {
				$product_name = $product_name . ' (' . $a['variant'] . ')';
			}
			$temp_data = array(
				'admin_fkid' => $a['admin_fkid'],
				'outlet_id' =>  $a['outlet_id'],
				'customer_name' => $a['customer_name'],
				'nomor_nota' => $display_nota,
				'tanggal' => millis_to_localtime('d-m-Y H:i:s', $a['tanggal']),
				'status' => $a['status'],
				'product_name' => $product_name,
				'sku' => $a['sku'],
				'pax' => $a['pax'],
				'outlet_name' => $a['outlet_name'],
				'shift_name' => $a['shift_name'],
				'qty_void' => $a['qty_void'],
				'price_void' => $a['price_void'],
				'sub_void' => $a['sub_void'],
				'info_void' => $a['info_void'],
				'id' => $a['id'],
				'employee' => $a['employee'],
			);
			array_push($dataArray, $temp_data);
		}
		$draw_json = array(
			'data' => $dataArray,
			"draw" => 1,
			"recordsFiltered" => count($dataArray),
			"recordsTotal" => count($dataArray),
		);
		return $this->response_json($draw_json);
	}

	public function generate_nota_print()
	{
		// Inisialisasi mPDF
		$mpdf = new Mpdf();

		$display_nota = $this->input->get("display_nota");
		$outlet_id = $this->input->get("outlet");
		$start_date = $this->input->get("start_date");
		$end_date = $this->input->get("end_date");

		$total_tax = 0;
		$sub_total = 0;
		$image_url = "";

		$data_nota = $this->history_model->get_data_print_nota($display_nota, $outlet_id, $start_date, $end_date)->result();
		$dummy_product_photo = "https://storage.googleapis.com/uniq-187911.appspot.com/public/default%20images/default_200x200.png";

		foreach ($data_nota as $value) {
			$total_tax += $value->tax;
			$sub_total += $value->price;

			$context = stream_context_create([
				"ssl" => [
					"verify_peer" => false,
					"verify_peer_name" => false,
				]
			]);

			$image_url = $value->product_image !== "" ? $value->product_image : $dummy_product_photo;

			$image_data = @file_get_contents($image_url, false, $context);
			$image_info = getimagesizefromstring($image_data);
			$mime = $image_info['mime'] ?? 'image/jpeg';
			$image_base64 = base64_encode($image_data);
			$value->product_image = "data:$mime;base64,$image_base64";
		}

		$outlet = $this->history_model->get_outlet_by_id($data_nota[0]->outlet_id)->row();
		if ($outlet->receipt_logo != "") {
			$imageData = @file_get_contents($outlet->receipt_logo);
			$image_info = getimagesizefromstring($imageData);
			$mime = $image_info['mime'] ?? 'image/jpeg';
			if ($imageData != false) {
				$base64 = base64_encode($imageData);
				$image_url = "data:$mime;base64,$base64";
			}
		}

		$data = [
			"outlet" => $outlet,
			"data_nota" => $data_nota,
			"total_tax" => $total_tax,
			"sub_total" => $sub_total,
			"image_url" => $image_url
		];

		ini_set('pcre.backtrack_limit', '10000000');

		// HTML untuk nota
		$html = $this->load->view("reports/sales/sales_history/template_nota", $data, true);

		// Tambahkan HTML ke mPDF
		$mpdf->WriteHTML($html);

		// Simpan atau Tampilkan PDF
		$mpdf->Output('nota_pembelian.pdf', 'I');
	}
}

/* End of file History.php */
/* Location: ./application/controllers/reports/History.php */