<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Report_cogs extends Auth_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('outlet/Outlets_model');
		$this->load->model('products/products_category/Products_category_model', 'category_model');
		$this->load->model('report/sales/M_report_cogs', 'm_cogs');
	}

	public function index()
	{
		$data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
		$data['from_select_category'] = $this->category_model->form_select();
		$data['url_detail'] = base_url("reports/sales/report_cogs/get_cogs_detail");
		// $this->template->view('report_cogs_v',$data); //menampilkan halaman
		$this->template->view('reports/sales/cogs/report_cogs_v', $data);
	}

	public function get_cogs_detail()
	{
		$data_post = $this->input->post();
		$data_detail = $this->m_cogs->get_cogs_detail($data_post)->result_array();

		foreach ($data_detail as $key => $value) {
			$qty = $value['qty'] - $value['qty_void'];
			$totalDis = $value['discount_sd'] + $value['discount_sales'] + $value['discount_tax'] + $value['voucher_tax'] + $value['voucher_sales'];
			$tax = $value['tax'] + $value['service_tax'];
			$promo = $value['promo'] + $value['promo_sales'];
			$subTotal = $value['price'] * $value['qty'] - $totalDis - $promo + $tax;
			$totalHpp = $value['hpp'] * $value['qty'];
			$data_detail[$key]['sub_total'] = $subTotal;
			$data_detail[$key]["profit"] = $subTotal - $totalHpp;
			$data_detail[$key]["disc"] = $totalDis;
			$data_detail[$key]["promo"] = $promo;
			$data_detail[$key]["total_hpp"] = $totalHpp;
			$data_detail[$key]["qty"] = $qty;
		}

		echo json_encode($data_detail);
		die();
	}

	public function get_data_v2($startDate, $endDate, $timeZone, $category, $outlet)
	{
		file_put_contents("php://stderr", "-------- get data v2-------\n");
		$dataSales = $this->m_cogs->data_cogs_v2($startDate, $endDate, $timeZone, $category, $outlet);

		$data = array();
		$pd_id_tpm = [];
		foreach ($dataSales as $key => $value) {
			$qty = $value['qty'] - $value['qty_void'];
			if ($qty == 0) {
				$qty = 1;
			}
			$subTotal = $value['total_price'];
			$tax = $value['tax'] + $value['service_tax'];
			$promo = $value['promo'] + $value['promo_sales'];
			$totalDis = $value['discount_sd'] + $value['discount_sales'] + $value['discount_tax'] + $value['voucher_tax'] + $value['voucher_sales'];
			$grandTotal = $subTotal - $totalDis + $tax - $promo;
			$totalHpp = $value['total_hpp'];
			$avgHpp = $value['total_hpp'] / $qty;
			$avgPrice = $value['total_price'] / $qty;
			// $subTotal = $value['sub_total'] -
			// $profit = $grandTotal-$totalHpp;
			// file_put_contents("php://stderr", "$avgHpp : ".$value['total_hpp'] ."/".$qty."\n");

			$tmpData = array(
				'pd_id' => $value['product_detail_id'],
				'nama_produk' => $value['product_name'],
				'sku' => $value['sku'] . $value['variant_sku'],
				'outlet' => $value['outlet_name'],
				'sub_category' => $value['sub_category'],
				'category' => $value['category'],
				'qty_sales' => $qty,
				'hpp' => $avgHpp,
				'total_hpp' => $totalHpp,
				'harga_jual' => $avgPrice,
				'sub_total' => $grandTotal,
				'tax' => $tax,
				'disc' => $totalDis,
				'promo' => $promo,
				'qty_purchase' => '0'
			);
			array_push($data, $tmpData);
			array_push($pd_id_tpm, $value['product_detail_id']);

			$grandTotalManual =  $value['total_price'] - $totalDis - (int)$value['promo'] + (int)$value['tax'];
			if ($grandTotal != $grandTotalManual) {
				// file_put_contents("php://stderr", "[ERROR CALCULATION] grandTotal not match " . $value['product_name']. " -> $grandTotal  VS  $grandTotalManual | ($avgHpp, $qty) - $totalDis - $value['promo'] + $value['tax'] \n");
				file_put_contents("php://stderr", "[ERROR CALCULATION] grandTotal not match " . $value['product_name'] . " -> $grandTotal  VS  $grandTotalManual | $subTotal-$totalDis+" . $value['tax'] . "+" . $value['service_tax'] . "-" . $value['promo'] . "\n");
			}
		}

		$pd_id = implode("','", $pd_id_tpm);

		// get purchase by date and pd_id
		// $purchase = $this->m_cogs->purchase($startDate,$endDate,$timeZone);
		// foreach ($purchase as $p) {
		// 	foreach ($data as $idx => $d) {
		// 		if ($d['pd_id']==$p['pd_id']) {
		// 			$data[$idx]['hpp'] = $p['price'];
		// 			$data[$idx]['total_hpp'] = $p['price']*$p['qty'];
		// 			$data[$idx]['qty_purchase'] = $p['qty'];
		// 		}
		// 	}
		// }

		// print_r($purchase);die;
		$draw_json = array(
			'data' => $data
		);
		return $this->output
			->set_content_type('application/json')
			->set_status_header(200)
			->set_output(json_encode($draw_json));
	}

	public function get_data_v3($startDate, $endDate, $timeZone, $category, $outlet)
	{
		file_put_contents("php://stderr", "-------- get data v3-------\n");

		// Get main sales data without tax information for better performance
		$dataSales = $this->m_cogs->data_cogs_v3($startDate, $endDate, $timeZone, $category, $outlet);

		$data = array();
		$pd_id_tpm = [];

		// Process main sales data first
		foreach ($dataSales as $key => $value) {
			$qty = $value['qty'] - $value['qty_void'];
			if ($qty == 0) {
				$qty = 1;
			}

			$totalDis = $value['discount_sd'] + $value['discount_sales'] + $value['voucher_sales'];
			$promo = $value['promo'] + $value['promo_sales'];
			$subTotal = $value['total_price'] - $totalDis - $promo;
			$totalHpp = $value['total_hpp'];

			$tmpData = array(
				'outlet_name' => $value['outlet_name'],
				'outlet' => $value['outlet_name'],
				'sku' => $value['sku'],
				'category' => $value['category'],
				'sub_category' => $value['sub_category'],
				'product_name' => $value['product_name'],
				'nama_produk' => $value['product_name'],
				'variant_sku' => $value['variant_sku'],
				'qty' => $qty,
				'qty_sales' => $qty,
				'pd_id' => $value['product_detail_id'],
				'hpp' => $totalHpp / $qty,
				'total_hpp' => $totalHpp,
				'harga_jual' => $value['total_price'] / $qty,
				'sub_total' => $subTotal,
				'tax' => 0, // Will be filled from separate tax query
				'discount_tax' => 0, // Will be filled from separate tax query
				'service_tax' => 0, // Will be filled from separate tax query
				'voucher_tax' => 0, // Will be filled from separate tax query
				'disc' => $totalDis,
				'promo' => $promo,
				'qty_purchase' => '0'
			);
			array_push($data, $tmpData);
			array_push($pd_id_tpm, $value['product_detail_id']);
		}

		// Get tax data separately for better performance
		if (!empty($pd_id_tpm)) {
			$taxData = $this->m_cogs->get_sales_detail_tax_v3($pd_id_tpm, $startDate, $endDate, $timeZone, $outlet);

			// Merge tax data with main data
			foreach ($data as $idx => $item) {
				$productDetailId = $item['pd_id'];
				if (isset($taxData[$productDetailId])) {
					$tax = $taxData[$productDetailId];
					$data[$idx]['tax'] = $tax['tax'];
					$data[$idx]['discount_tax'] = $tax['discount_tax'];
					$data[$idx]['service_tax'] = $tax['service_tax'];
					$data[$idx]['voucher_tax'] = $tax['voucher_tax'];

					// Recalculate totals with tax information
					$totalTax = $tax['tax'] + $tax['service_tax'];
					$totalDiscount = $data[$idx]['disc'] + $tax['discount_tax'] + $tax['voucher_tax'];
					$data[$idx]['sub_total'] = $item['harga_jual'] * $item['qty'] - $totalDiscount - $item['promo'] + $totalTax;
				}
			}
		}

		$draw_json = array(
			'data' => $data
		);
		return $this->output
			->set_content_type('application/json')
			->set_status_header(200)
			->set_output(json_encode($draw_json));
	}

	public function get_data($startDate, $endDate, $timeZone, $category, $outlet)
	{
		$dataSales = $this->m_cogs->data_cogs($startDate, $endDate, $timeZone, $category, $outlet);

		$data = array();
		$pd_id_tpm = [];
		foreach ($dataSales as $key => $value) {
			$qty = $value['qty'] - $value['qty_void'];
			$subTotal = $qty * $value['price'];
			$totalDis = $value['discout_sd'] + $value['discount_sales'] + $value['discount_tax'];
			$grandTotal = $subTotal - $totalDis + $value['tax'] + $value['service_tax'] - $value['promo'] - $value['voucher_tax'];
			$totalHpp = $qty * $value['hpp'];
			// $subTotal = $value['sub_total'] -
			// $profit = $grandTotal-$totalHpp;
			$tmpData = array(
				'pd_id' => $value['product_detail_id'],
				'nama_produk' => $value['product_name'],
				'sku' => $value['sku'] . $value['variant_sku'],
				'outlet' => $value['outlet_name'],
				'sub_category' => $value['sub_category'],
				'category' => $value['category'],
				'qty_sales' => $qty,
				'hpp' => '0',
				'total_hpp' => $totalHpp,
				'harga_jual' => $value['price'],
				'sub_total' => $value['sub_total'],
				'tax' => $value['tax'],
				'disc' => $totalDis,
				'promo' => $value['promo'],
				'qty_purchase' => '0'
			);
			array_push($data, $tmpData);
			array_push($pd_id_tpm, $value['product_detail_id']);
		}

		$pd_id = implode("','", $pd_id_tpm);

		// get purchase by date and pd_id
		// $purchase = $this->m_cogs->purchase($startDate,$endDate,$timeZone);
		// foreach ($purchase as $p) {
		// 	foreach ($data as $idx => $d) {
		// 		if ($d['pd_id']==$p['pd_id']) {
		// 			$data[$idx]['hpp'] = $p['price'];
		// 			$data[$idx]['total_hpp'] = $p['price']*$p['qty'];
		// 			$data[$idx]['qty_purchase'] = $p['qty'];
		// 		}
		// 	}
		// }

		// print_r($purchase);die;
		$draw_json = array(
			'data' => $data
		);
		return $this->output
			->set_content_type('application/json')
			->set_status_header(200)
			->set_output(json_encode($draw_json));
	}
}

/* End of file Report_cogs.php */
/* Location: ./application/modules/cogs/controllers/Report_cogs.php */
